import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  ShoppingBag,
  ChevronLeft,
  Minus,
  Plus,
  Trash2,
  CreditCard,
  ShieldCheck,
  Truck,
  CheckCircle,
  Mail
} from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { dataService } from '../services/dataService';

function CartPage() {
  const navigate = useNavigate();
  const { items, removeFromCart, updateQuantity, clearCart, subtotal } = useCart();
  const { user, isAuthenticated } = useAuth();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [animateTotal, setAnimateTotal] = useState(false);
  const [orderConfirmed, setOrderConfirmed] = useState(false);
  const [orderDetails, setOrderDetails] = useState(null);

  useEffect(() => {
    setAnimateTotal(true);
    const timer = setTimeout(() => setAnimateTotal(false), 500);
    return () => clearTimeout(timer);
  }, [subtotal]);

  const handleCheckout = async () => {
    if (!isAuthenticated || !user) {
      alert('Please login to complete your order');
      navigate('/login');
      return;
    }

    if (items.length === 0) {
      alert('Your cart is empty');
      return;
    }

    setIsCheckingOut(true);

    try {
      // Calculate totals
      const tax = subtotal * 0.07;
      const total = subtotal + tax;

      // Prepare order data
      const orderData = {
        customer_name: user.name || `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Customer',
        shipping_address: {
          street: '123 Demo Street',
          city: 'Demo City',
          state: 'Demo State',
          zip: '12345',
          country: 'India'
        },
        billing_address: {
          street: '123 Demo Street',
          city: 'Demo City',
          state: 'Demo State',
          zip: '12345',
          country: 'India'
        },
        payment_method: 'demo_payment',
        shipping_amount: 0,
        order_notes: 'Demo order from cart page'
      };

      console.log('🛒 Creating order with data:', orderData);

      // Create order via API
      const order = await dataService.createOrder(user.id, orderData);

      console.log('✅ Order created successfully:', order);

      // Set order details for confirmation
      setOrderDetails({
        order_number: order.order_number,
        total_amount: total,
        items: items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          color: item.color,
          size: item.size,
          total: item.price * item.quantity
        })),
        customer_email: user.email,
        customer_name: orderData.customer_name
      });

      // Clear cart and show confirmation
      clearCart();
      setOrderConfirmed(true);

    } catch (error) {
      console.error('🚨 Order creation failed:', error);
      alert('Failed to create order. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  const handleRemoveItem = (itemId, color, size) => {
    removeFromCart(itemId, color, size);
    setShowDeleteConfirm(null);
  };

  const handleQuantityDecrease = (item) => {
    if (item.quantity > 1) {
      updateQuantity(item.id, item.quantity - 1, item.color, item.size);
    }
  };

  const handleQuantityIncrease = (item) => {
    updateQuantity(item.id, item.quantity + 1, item.color, item.size);
  };

  const tax = subtotal * 0.07;
  const total = subtotal + tax;

  // Order Confirmation Component
  if (orderConfirmed && orderDetails) {
    return (
      <div className="min-h-screen bg-[#000000]">
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="bg-green-500 p-6 rounded-full">
                <CheckCircle size={48} className="text-white" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Order Confirmed!
            </h1>
            <p className="text-[#AAAAAA] text-lg mb-2">
              Thank you for your order, {orderDetails.customer_name}
            </p>
            <p className="text-[#AAAAAA] mb-6">
              Order #{orderDetails.order_number}
            </p>
          </div>

          <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#404040] mb-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Mail size={20} />
              Confirmation Email Sent
            </h2>
            <p className="text-[#AAAAAA] mb-4">
              A confirmation email has been sent to <span className="text-white font-medium">{orderDetails.customer_email}</span> with your order details and tracking information.
            </p>
            <div className="bg-[#2a2a2a] p-4 rounded-lg">
              <h3 className="text-white font-medium mb-3">Order Summary</h3>
              <div className="space-y-2">
                {orderDetails.items.map((item, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">
                      {item.name} ({item.color}, {item.size}) x {item.quantity}
                    </span>
                    <span className="text-white">${item.total.toFixed(2)}</span>
                  </div>
                ))}
                <div className="border-t border-[#404040] pt-2 mt-2">
                  <div className="flex justify-between font-semibold">
                    <span className="text-white">Total</span>
                    <span className="text-white">${orderDetails.total_amount.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => navigate('/orders')}
              className="px-6 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white font-medium rounded-lg hover:from-[#1a3f9e] hover:to-[#4a9dd4] transition-all duration-300"
            >
              View Order History
            </button>
            <button
              onClick={() => navigate('/collections')}
              className="px-6 py-3 bg-[#2a2a2a] text-white font-medium rounded-lg hover:bg-[#404040] transition-colors border border-[#404040]"
            >
              Continue Shopping
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#000000]">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Mobile Header */}
        <div className="mb-6 flex items-center justify-between">
          <button
            onClick={() => navigate(-1)}
            className="text-[#AAAAAA] hover:text-white flex items-center gap-2 transition-all duration-300 group"
          >
            <div className="bg-[#2a2a2a] p-2 rounded-full group-hover:bg-[#404040] transition-colors">
              <ChevronLeft size={16} />
            </div>
            <span className="font-medium hidden sm:inline">Continue Shopping</span>
          </button>
          {/* Mobile trust badges */}
          <div className="flex items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <ShieldCheck size={16} className="text-emerald-500" />
              <span className="hidden sm:inline">Secure</span>
            </div>
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <Truck size={16} className="text-amber-500" />
              <span className="hidden sm:inline">Free Ship</span>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="mb-8 border-b border-[#2a2a2a] pb-6">
          <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2 tracking-tighter">
            Your <span className="text-[#AAAAAA]">Cart</span>
          </h1>
          <p className="text-[#AAAAAA] text-sm sm:text-base">
            {items.length} {items.length === 1 ? 'item' : 'items'} in your shopping bag
          </p>
        </div>

        {items.length === 0 ? (
          <div className="text-center py-16 bg-[#1a1a1a] rounded-xl border border-[#404040] backdrop-blur-sm shadow-xl">
            <div className="flex justify-center mb-6">
              <div className="bg-[#2a2a2a] p-6 rounded-full">
                <ShoppingBag size={40} className="text-[#AAAAAA]" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl text-white mb-4 font-bold">Your cart is empty</h2>
            <p className="text-[#AAAAAA] mb-8 max-w-md mx-auto text-sm sm:text-base px-4">
              Looks like you haven't added anything to your cart yet. Discover our premium selection and find something amazing.
            </p>
            <button
              onClick={() => navigate('/collections')}
              className="px-8 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] hover:from-[#1a3f9e] hover:to-[#4a9dd4] text-white font-medium rounded-lg transition-all duration-300 shadow-lg"
            >
              Start Shopping
            </button>
          </div>
        ) : (
          <div className="space-y-6 lg:grid lg:grid-cols-12 lg:gap-8 lg:space-y-0">
            {/* Cart Items - Mobile First */}
            <div className="lg:col-span-8">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl overflow-hidden shadow-xl border border-[#404040]">
                {/* Desktop Header - Hidden on Mobile */}
                <div className="hidden md:block p-6 border-b border-[#2a2a2a]">
                  <div className="flex justify-between text-[#AAAAAA] text-sm font-medium">
                    <span className="uppercase tracking-wider">Product</span>
                    <div className="grid grid-cols-3 gap-4 w-[260px]">
                      <span className="text-center uppercase tracking-wider">Price</span>
                      <span className="text-center uppercase tracking-wider">Qty</span>
                      <span className="text-center uppercase tracking-wider">Total</span>
                    </div>
                  </div>
                </div>

                {/* Cart Items */}
                {items.map((item) => (
                  <div
                    key={`${item.id}-${item.color}-${item.size}`}
                    className="p-4 sm:p-6 border-b border-[#2a2a2a] hover:bg-[#2a2a2a] transition-colors duration-300"
                  >
                    {/* Mobile Layout */}
                    <div className="md:hidden">
                      <div className="flex gap-4 mb-4">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg flex-shrink-0 hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        </Link>
                        <div className="flex-1 min-w-0">
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white font-medium text-base sm:text-lg mb-2 truncate hover:text-cyan-400 transition-colors">
                              {item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0">
                          <div className="text-white font-medium text-lg mb-2">
                            ${item.price.toFixed(2)}
                          </div>
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1 justify-end">
                              <button
                                className="bg-red-600 text-white p-2 rounded-md hover:bg-red-700 transition-colors active:scale-95"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={14} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-2 rounded-md hover:bg-[#6a6a6a] transition-colors active:scale-95"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={14} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-[#AAAAAA] hover:text-red-500 transition-colors p-2 hover:scale-110 active:scale-95"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                      {/* Mobile Quantity Controls */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={16} />
                          </button>
                          <div className="w-12 h-10 bg-[#2a2a2a] flex items-center justify-center text-white font-medium">
                            {item.quantity}
                          </div>
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={16} />
                          </button>
                        </div>
                        <div className="text-white font-semibold text-lg">
                          ${(item.price * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden md:flex md:items-center justify-between">
                      <div className="flex items-center gap-6">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-24 h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        </Link>
                        <div>
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white hover:text-cyan-400 font-medium text-lg transition-colors duration-300 cursor-pointer">
                              {item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm mt-2 space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 items-center w-[260px]">
                        <div className="text-white text-center font-medium">
                          ${item.price.toFixed(2)}
                        </div>
                        <div className="flex items-center justify-center">
                          <button
                            className="w-8 h-8 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={14} />
                          </button>
                          <div className="w-10 h-8 bg-[#2a2a2a] flex items-center justify-center text-white font-medium">
                            {item.quantity}
                          </div>
                          <button
                            className="w-8 h-8 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={14} />
                          </button>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-white font-medium">
                            ${(item.price * item.quantity).toFixed(2)}
                          </span>
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1">
                              <button
                                className="bg-red-600 text-white p-1 rounded-md hover:bg-red-700 transition-colors"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={16} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-1 rounded-md hover:bg-[#6a6a6a] transition-colors"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={16} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-[#AAAAAA] hover:text-red-500 transition-colors p-1 hover:scale-110 active:scale-95"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={18} />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="p-4 sm:p-6 flex justify-end">
                  <button
                    onClick={clearCart}
                    className="text-[#AAAAAA] hover:text-red-500 text-sm flex items-center gap-1 transition-colors duration-300 p-2"
                  >
                    <Trash2 size={14} />
                    <span>Clear cart</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-4">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl p-4 sm:p-6 border border-[#404040] shadow-xl lg:sticky lg:top-8">
                <h2 className="text-xl sm:text-2xl font-semibold text-white mb-6 sm:mb-8 border-b border-[#2a2a2a] pb-4">
                  Order Summary
                </h2>
                <div className="space-y-3 sm:space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Subtotal</span>
                    <span className="text-white font-medium">${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Shipping</span>
                    <span className="text-white font-medium">Free</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Tax (7%)</span>
                    <span className="text-white font-medium">${tax.toFixed(2)}</span>
                  </div>
                </div>
                <div className="border-t border-[#2a2a2a] pt-4 sm:pt-6 mb-6 sm:mb-8">
                  <div className="flex justify-between text-lg sm:text-xl font-semibold">
                    <span className="text-white">Total</span>
                    <span
                      className={`text-white transition-all duration-500 ${
                        animateTotal ? 'scale-110 text-gray-300' : ''
                      }`}
                    >
                      ${total.toFixed(2)}
                    </span>
                  </div>
                </div>
                <button
                  className={`w-full py-4 bg-[#FF6F35] hover:bg-gray-200 text-white font-medium rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-lg mb-6 text-base sm:text-lg ${
                    isCheckingOut ? 'opacity-75 cursor-wait' : 'hover:scale-105 active:scale-95'
                  }`}
                  onClick={handleCheckout}
                  disabled={isCheckingOut}
                >
                  {isCheckingOut ? (
                    <>
                      <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin " />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard size={18}/>
                      Complete Order
                    </>
                  )}
                </button>
                {/* Payment Methods */}
<div className="grid grid-cols-3 gap-3">
  {/* Visa */}
<div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
  <img
    src="https://static.cdnlogo.com/logos/v/34/visa.svg"
    alt="Visa"
    className="h-6 w-auto"
  />
</div>

  {/* Mastercard */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Mastercard-logo.svg"
      alt="Mastercard"
      className="h-6"
    />
  </div>

  {/* PayPal */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
      alt="PayPal"
      className="h-6"
    />
  </div>
</div>
                {/* Trust Badges */}
                <div className="space-y-3 sm:space-y-4">
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <ShieldCheck size={18} className="text-emerald-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">100% Secure Checkout</span>
                      <br />All transactions are secured and encrypted
                    </p>
                  </div>
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <Truck size={18} className="text-amber-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">Free Shipping</span>
                      <br />On all orders over $50
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CartPage;