import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI } from '../services/authAPI';

// Auth Context
const AuthContext = createContext();

// Auth Actions
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SEND_OTP_START: 'SEND_OTP_START',
  SEND_OTP_SUCCESS: 'SEND_OTP_SUCCESS',
  SEND_OTP_FAILURE: 'SEND_OTP_FAILURE',
  VERIFY_OTP_START: 'VERIFY_OTP_START',
  VERIFY_OTP_SUCCESS: 'VERIFY_OTP_SUCCESS',
  VERIFY_OTP_FAILURE: 'VERIFY_OTP_FAILURE',
  UPDATE_PROFILE_START: 'UPDATE_PROFILE_START',
  UPDATE_PROFILE_SUCCESS: 'UPDATE_PROFILE_SUCCESS',
  UPDATE_PROFILE_FAILURE: 'UPDATE_PROFILE_FAILURE',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_LOADING: 'SET_LOADING'
};

// Initial State
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  otpSent: false,
  otpPhone: null,
  originalPhone: null, // Store the original phone number for verification
  tokens: {
    accessToken: null,
    refreshToken: null
  }
};

// Auth Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
    case AUTH_ACTIONS.SEND_OTP_START:
    case AUTH_ACTIONS.VERIFY_OTP_START:
    case AUTH_ACTIONS.UPDATE_PROFILE_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case AUTH_ACTIONS.SEND_OTP_SUCCESS:
      return {
        ...state,
        isLoading: false,
        otpSent: true,
        otpPhone: action.payload.phone,
        originalPhone: action.payload.originalPhone, // Store original phone
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
    case AUTH_ACTIONS.VERIFY_OTP_SUCCESS:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        tokens: action.payload.tokens,
        otpSent: false,
        otpPhone: null,
        originalPhone: null,
        error: null
      };

    case AUTH_ACTIONS.UPDATE_PROFILE_SUCCESS:
      return {
        ...state,
        isLoading: false,
        user: action.payload.profile,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
    case AUTH_ACTIONS.SEND_OTP_FAILURE:
    case AUTH_ACTIONS.VERIFY_OTP_FAILURE:
    case AUTH_ACTIONS.UPDATE_PROFILE_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload.error,
        otpSent: action.type === AUTH_ACTIONS.SEND_OTP_FAILURE ? false : state.otpSent
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };

    default:
      return state;
  }
};

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from localStorage on app start
  useEffect(() => {
    const loadStoredAuth = () => {
      try {
        const storedTokens = localStorage.getItem('wolffoxx_tokens');
        const storedUser = localStorage.getItem('wolffoxx_user');

        if (storedTokens && storedUser) {
          const tokens = JSON.parse(storedTokens);
          const user = JSON.parse(storedUser);

          // Check if tokens are still valid (basic check)
          if (tokens.accessToken && user) {
            dispatch({
              type: AUTH_ACTIONS.LOGIN_SUCCESS,
              payload: { user, tokens }
            });
          }
        }
      } catch (error) {
        console.error('Error loading stored auth:', error);
        // Clear invalid stored data
        localStorage.removeItem('wolffoxx_tokens');
        localStorage.removeItem('wolffoxx_user');
      }
    };

    loadStoredAuth();
  }, []);

  // Save auth data to localStorage
  const saveAuthData = (user, tokens) => {
    try {
      localStorage.setItem('wolffoxx_tokens', JSON.stringify(tokens));
      localStorage.setItem('wolffoxx_user', JSON.stringify(user));
    } catch (error) {
      console.error('Error saving auth data:', error);
    }
  };

  // Clear auth data from localStorage
  const clearAuthData = () => {
    localStorage.removeItem('wolffoxx_tokens');
    localStorage.removeItem('wolffoxx_user');
  };

  // Clear all user-specific data
  const clearAllUserData = () => {
    // Clear auth data
    clearAuthData();

    // Clear all localStorage data that might contain user-specific information
    localStorage.removeItem('cart');
    localStorage.removeItem('wishlist');
    localStorage.removeItem('outfits');
    localStorage.removeItem('currentOutfit');

    // Note: Context clear functions will be called separately
    // since we can't access other contexts from AuthContext directly
  };

  // Send OTP
  const sendOTP = async (phone) => {
    console.log('AuthContext sendOTP called with:', phone);
    dispatch({ type: AUTH_ACTIONS.SEND_OTP_START });

    try {
      const response = await authAPI.sendOTP(phone);
      console.log('AuthContext sendOTP success:', response);

      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_SUCCESS,
        payload: {
          phone: response.phone,
          originalPhone: phone // Store the original phone number
        }
      });

      return { success: true, data: response };
    } catch (error) {
      console.log('AuthContext sendOTP error:', error.message);
      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Verify OTP and Login
  const verifyOTP = async (phone, otp, userDetails = {}) => {
    dispatch({ type: AUTH_ACTIONS.VERIFY_OTP_START });

    try {
      const response = await authAPI.verifyOTP(phone, otp, userDetails);

      const { user, tokens } = response;

      // Save to localStorage
      saveAuthData(user, tokens);

      dispatch({
        type: AUTH_ACTIONS.VERIFY_OTP_SUCCESS,
        payload: { user, tokens }
      });

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.VERIFY_OTP_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Resend OTP
  const resendOTP = async (phone) => {
    dispatch({ type: AUTH_ACTIONS.SEND_OTP_START });

    try {
      const response = await authAPI.resendOTP(phone);

      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_SUCCESS,
        payload: {
          phone: response.phone,
          originalPhone: phone // Store the original phone number
        }
      });

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Update Profile
  const updateProfile = async (profileData) => {
    dispatch({ type: AUTH_ACTIONS.UPDATE_PROFILE_START });

    try {
      const response = await authAPI.updateProfile(profileData, state.tokens?.accessToken);

      // Update stored user data
      saveAuthData(response.profile, state.tokens);

      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE_SUCCESS,
        payload: { profile: response.profile }
      });

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Logout
  const logout = async () => {
    try {
      // Call logout API if needed
      if (state.tokens?.accessToken) {
        await authAPI.logout(state.tokens.accessToken);
      }
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Clear all user data regardless of API call result
      clearAllUserData();
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  // Clear Error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Get fresh user profile
  const refreshProfile = async () => {
    if (!state.tokens?.accessToken) return;

    try {
      const response = await authAPI.getProfile(state.tokens.accessToken);

      // Update stored user data
      saveAuthData(response, state.tokens);

      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE_SUCCESS,
        payload: { profile: response }
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('Refresh profile error:', error);
      return { success: false, error: error.message };
    }
  };

  const value = {
    // State
    ...state,

    // Actions
    sendOTP,
    verifyOTP,
    resendOTP,
    updateProfile,
    logout,
    clearError,
    refreshProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
