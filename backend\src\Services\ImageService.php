<?php

namespace Wolffoxx\Services;

use Wolffoxx\Utils\Logger;
use Intervention\Image\ImageManagerStatic as Image;

/**
 * Image Service
 * 
 * Handles image upload, processing, optimization,
 * and storage management.
 */
class ImageService
{
    private Logger $logger;
    private string $uploadPath;
    private array $imageSizes;
    private array $allowedTypes;
    private int $maxFileSize;

    public function __construct()
    {
        $this->logger = new Logger('image');
        $this->uploadPath = __DIR__ . '/../../storage/uploads/';
        
        // Define image sizes for responsive images
        $this->imageSizes = [
            'thumbnail' => ['width' => 300, 'height' => 300],
            'medium' => ['width' => 600, 'height' => 600],
            'large' => ['width' => 1200, 'height' => 1200],
            'original' => ['width' => null, 'height' => null] // Keep original
        ];

        $this->allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $this->maxFileSize = (int)($_ENV['UPLOAD_MAX_SIZE'] ?? 5242880); // 5MB

        // Ensure upload directories exist
        $this->createDirectories();
    }

    /**
     * Upload and process product image
     */
    public function uploadProductImage(array $file, int $productId): array
    {
        try {
            // Validate file
            $validation = $this->validateImage($file);
            if (!$validation['valid']) {
                throw new \Exception($validation['error']);
            }

            // Generate unique filename
            $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $baseFilename = 'product_' . $productId . '_' . time() . '_' . uniqid();

            // Create different sizes
            $imageUrls = [];
            $originalImage = Image::make($file['tmp_name']);

            foreach ($this->imageSizes as $sizeName => $dimensions) {
                $filename = $baseFilename . '_' . $sizeName . '.' . $extension;
                $filepath = $this->uploadPath . 'products/' . $filename;

                if ($sizeName === 'original') {
                    // Save original without resizing
                    $originalImage->save($filepath, 90);
                } else {
                    // Resize and save
                    $resized = clone $originalImage;
                    $resized->fit($dimensions['width'], $dimensions['height'], function ($constraint) {
                        $constraint->upsize(); // Prevent upsizing
                    });
                    $resized->save($filepath, 90);
                }

                $imageUrls[$sizeName] = '/storage/uploads/products/' . $filename;

                // Also create WebP version for better performance
                if ($extension !== 'webp') {
                    $webpFilename = $baseFilename . '_' . $sizeName . '.webp';
                    $webpFilepath = $this->uploadPath . 'products/' . $webpFilename;
                    
                    if ($sizeName === 'original') {
                        $originalImage->save($webpFilepath, 90, 'webp');
                    } else {
                        $resized->save($webpFilepath, 90, 'webp');
                    }
                    
                    $imageUrls[$sizeName . '_webp'] = '/storage/uploads/products/' . $webpFilename;
                }
            }

            // Get image metadata
            $metadata = [
                'original_name' => $file['name'],
                'file_size' => $file['size'],
                'width' => $originalImage->width(),
                'height' => $originalImage->height(),
                'format' => $extension,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->logger->info('Product image uploaded successfully', [
                'product_id' => $productId,
                'filename' => $baseFilename,
                'sizes_created' => array_keys($this->imageSizes),
                'metadata' => $metadata
            ]);

            return [
                'urls' => $imageUrls,
                'metadata' => $metadata
            ];

        } catch (\Exception $e) {
            $this->logger->error('Product image upload failed', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Upload profile image
     */
    public function uploadProfileImage(array $file, int $userId): array
    {
        try {
            // Validate file
            $validation = $this->validateImage($file);
            if (!$validation['valid']) {
                throw new \Exception($validation['error']);
            }

            // Generate filename
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = 'profile_' . $userId . '_' . time() . '.' . $extension;
            $filepath = $this->uploadPath . 'profiles/' . $filename;

            // Process image
            $image = Image::make($file['tmp_name']);
            
            // Resize to standard profile size (400x400)
            $image->fit(400, 400, function ($constraint) {
                $constraint->upsize();
            });

            // Save optimized image
            $image->save($filepath, 85);

            // Create WebP version
            $webpFilename = 'profile_' . $userId . '_' . time() . '.webp';
            $webpFilepath = $this->uploadPath . 'profiles/' . $webpFilename;
            $image->save($webpFilepath, 85, 'webp');

            $imageUrl = '/storage/uploads/profiles/' . $filename;
            $webpUrl = '/storage/uploads/profiles/' . $webpFilename;

            $this->logger->info('Profile image uploaded successfully', [
                'user_id' => $userId,
                'filename' => $filename
            ]);

            return [
                'url' => $imageUrl,
                'webp_url' => $webpUrl,
                'metadata' => [
                    'file_size' => filesize($filepath),
                    'width' => 400,
                    'height' => 400,
                    'format' => $extension
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->error('Profile image upload failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Delete image files
     */
    public function deleteImage(string $imagePath): bool
    {
        try {
            $fullPath = $this->uploadPath . ltrim($imagePath, '/storage/uploads/');
            
            if (file_exists($fullPath)) {
                unlink($fullPath);
                
                // Also delete WebP version if exists
                $webpPath = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $fullPath);
                if (file_exists($webpPath)) {
                    unlink($webpPath);
                }
                
                $this->logger->info('Image deleted successfully', [
                    'path' => $imagePath
                ]);
                
                return true;
            }

            return false;

        } catch (\Exception $e) {
            $this->logger->error('Image deletion failed', [
                'path' => $imagePath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Validate uploaded image
     */
    private function validateImage(array $file): array
    {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'error' => 'File upload error: ' . $this->getUploadErrorMessage($file['error'])
            ];
        }

        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return [
                'valid' => false,
                'error' => 'File size exceeds maximum allowed size of ' . ($this->maxFileSize / 1024 / 1024) . 'MB'
            ];
        }

        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedTypes)) {
            return [
                'valid' => false,
                'error' => 'File type not allowed. Allowed types: ' . implode(', ', $this->allowedTypes)
            ];
        }

        // Verify it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return [
                'valid' => false,
                'error' => 'File is not a valid image'
            ];
        }

        // Check image dimensions (minimum size)
        if ($imageInfo[0] < 100 || $imageInfo[1] < 100) {
            return [
                'valid' => false,
                'error' => 'Image dimensions too small. Minimum 100x100 pixels required'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Create necessary directories
     */
    private function createDirectories(): void
    {
        $directories = [
            'products',
            'profiles',
            'temp'
        ];

        foreach ($directories as $dir) {
            $fullPath = $this->uploadPath . $dir;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
            }
        }
    }

    /**
     * Get upload error message
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        return match ($errorCode) {
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension',
            default => 'Unknown upload error'
        };
    }

    /**
     * Get optimized image URL based on device/context
     */
    public function getOptimizedImageUrl(string $baseUrl, string $size = 'medium', bool $webp = true): string
    {
        if ($webp && $this->supportsWebP()) {
            return str_replace('.' . pathinfo($baseUrl, PATHINFO_EXTENSION), '_' . $size . '.webp', $baseUrl);
        }
        
        return str_replace('.' . pathinfo($baseUrl, PATHINFO_EXTENSION), '_' . $size . '.' . pathinfo($baseUrl, PATHINFO_EXTENSION), $baseUrl);
    }

    /**
     * Check if client supports WebP
     */
    private function supportsWebP(): bool
    {
        $accept = $_SERVER['HTTP_ACCEPT'] ?? '';
        return strpos($accept, 'image/webp') !== false;
    }

    /**
     * Get image storage statistics
     */
    public function getStorageStats(): array
    {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'by_type' => []
        ];

        $directories = ['products', 'profiles'];
        
        foreach ($directories as $dir) {
            $path = $this->uploadPath . $dir;
            if (is_dir($path)) {
                $files = glob($path . '/*');
                $dirStats = [
                    'files' => count($files),
                    'size' => 0
                ];
                
                foreach ($files as $file) {
                    if (is_file($file)) {
                        $dirStats['size'] += filesize($file);
                    }
                }
                
                $stats['by_type'][$dir] = $dirStats;
                $stats['total_files'] += $dirStats['files'];
                $stats['total_size'] += $dirStats['size'];
            }
        }

        return $stats;
    }
}
