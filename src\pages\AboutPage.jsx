import { motion, useScroll, useTransform } from 'framer-motion';
import { useState, useRef, useEffect } from 'react';
import { ArrowRight, Instagram, Twitter, Users, Award, Globe, Heart } from 'lucide-react';

// Custom MarqueeText component
const MarqueeText = ({ text, speed = 50, className = "" }) => {
  return (
    <div className={`overflow-hidden whitespace-nowrap ${className}`}>
      <motion.div
        className="inline-block"
        animate={{ x: ["100%", "-100%"] }}
        transition={{
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: speed,
            ease: "linear",
          },
        }}
      >
        <span className="text-lg font-bold tracking-wider px-8">{text}</span>
      </motion.div>
    </div>
  );
};

// Floating elements animation
const FloatingElement = ({ children, delay = 0 }) => (
  <motion.div
    animate={{
      y: [0, -10, 0],
      rotate: [0, 1, 0],
    }}
    transition={{
      duration: 6,
      repeat: Infinity,
      delay,
    }}
  >
    {children}
  </motion.div>
);

// Parallax section component
const ParallaxSection = ({ children, offset = 50 }) => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, offset]);

  return (
    <motion.div ref={ref} style={{ y }}>
      {children}
    </motion.div>
  );
};

export default function WollfoxxAboutPage() {
  const [isVisible, setIsVisible] = useState({});
  const containerRef = useRef(null);

  const stats = [
    { icon: Users, number: "50K+", label: "Community Members" },
    { icon: Award, number: "100+", label: "Unique Designs" },
    { icon: Globe, number: "25+", label: "Countries Served" },
    { icon: Heart, number: "98%", label: "Customer Love" }
  ];

  const founders = [
    {
      name: "Alex Rivera",
      role: "Creative Director & Co-Founder",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=400&auto=format&fit=crop",
      bio: "Visionary designer with 8+ years in streetwear culture"
    },
    {
      name: "Jordan Chen",
      role: "Brand Director & Co-Founder",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=400&auto=format&fit=crop",
      bio: "Street culture enthusiast and brand strategist"
    }
  ];

  const socialFeed = [
    "https://images.unsplash.com/photo-1618354691373-d851c5c3a990?q=80&w=300&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1576566588028-4147f3842f27?q=80&w=300&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1578897366846-aa7aaffa3c18?q=80&w=300&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1584670747135-5b77a3e6222f?q=80&w=300&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1551232864-3f0890e580d9?q=80&w=300&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1575428652377-a2d80e2277fc?q=80&w=300&auto=format&fit=crop"
  ];

  return (
    <div className="bg-black text-white overflow-hidden" ref={containerRef}>
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#0f172a] via-[#1e293b] to-black"></div>
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-[#214FC3]/10 to-[#54AEE1]/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-[#54AEE1]/10 to-[#14b8a6]/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
          }}
        />
      </div>

      {/* Marquee */}
      <div className="relative z-10 border-b border-[#214FC3]/30 bg-[#0f172a]/50 backdrop-blur-sm">
        <MarqueeText
          text="PREMIUM STREETWEAR • OVERSIZED PERFECTION • WOLLFOXX CULTURE • AUTHENTIC DESIGN • QUALITY CRAFTED"
          speed={60}
          className="py-4 text-[#54AEE1]"
        />
      </div>

      {/* Hero Section */}
      <section className="relative z-10 min-h-screen flex items-center justify-center px-4 py-20">
        <ParallaxSection offset={-50}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1.2, ease: "easeOut" }}
            className="text-center max-w-6xl mx-auto"
          >
            <FloatingElement>
              <motion.h1
                className="font-black text-7xl md:text-9xl lg:text-[12rem] leading-none mb-8 bg-gradient-to-r from-white via-[#54AEE1] to-[#14b8a6] bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.2 }}
              >
                WOLL<span className="text-[#214FC3]">FOXX</span>
              </motion.h1>
            </FloatingElement>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="space-y-6"
            >
              <p className="text-2xl md:text-3xl text-gray-300 font-light max-w-3xl mx-auto">
                Born from passion, crafted for rebels.
              </p>
              <p className="text-lg text-gray-400 max-w-2xl mx-auto">
                Two visionaries, one mission: to redefine streetwear through premium oversized aesthetics that speak without words.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1 }}
              className="mt-12"
            >
              <button className="group relative px-12 py-4 bg-gradient-to-r from-[#214FC3] to-[#54AEE1] rounded-full font-bold text-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-[#214FC3]/25">
                <span className="relative z-10 flex items-center gap-3">
                  DISCOVER OUR STORY
                  <ArrowRight className="group-hover:translate-x-1 transition-transform" size={20} />
                </span>
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-[#54AEE1] to-[#14b8a6]"
                  initial={{ x: "100%" }}
                  whileHover={{ x: 0 }}
                  transition={{ duration: 0.3 }}
                />
              </button>
            </motion.div>
          </motion.div>
        </ParallaxSection>
      </section>

      {/* Stats Section */}
      <section className="relative z-10 py-20 border-y border-[#214FC3]/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center group"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-[#214FC3]/20 to-[#54AEE1]/20 rounded-full mb-4 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="text-[#54AEE1]" size={28} />
                </div>
                <motion.h3
                  className="text-3xl md:text-4xl font-bold text-white mb-2"
                  initial={{ scale: 0.5 }}
                  whileInView={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                >
                  {stat.number}
                </motion.h3>
                <p className="text-gray-400 font-medium">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Founders Section */}
      <section className="relative z-10 py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <h2 className="text-5xl md:text-7xl font-black mb-6 bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">
              THE <span className="text-[#214FC3]">VISIONARIES</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Meet the duo behind the revolution. Two minds, one vision: to create streetwear that transcends fashion.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12 max-w-5xl mx-auto">
            {founders.map((founder, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="group"
              >
                <div className="relative overflow-hidden rounded-2xl mb-6">
                  <motion.img
                    src={founder.image}
                    alt={founder.name}
                    className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                    whileHover={{ scale: 1.05 }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 + 0.4 }}
                >
                  <h3 className="text-2xl font-bold text-white mb-2">{founder.name}</h3>
                  <p className="text-[#54AEE1] font-medium mb-4">{founder.role}</p>
                  <p className="text-gray-300">{founder.bio}</p>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Brand Story */}
      <section className="relative z-10 py-24 bg-gradient-to-r from-[#0f172a]/10 to-[#1e293b]/10">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <ParallaxSection offset={30}>
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
              >
                <h2 className="text-4xl md:text-6xl font-black mb-8 text-white">
                  BORN FROM THE <span className="text-[#214FC3]">STREETS</span>
                </h2>
                <div className="space-y-6 text-gray-300 text-lg leading-relaxed">
                  <p>
                    In 2020, two friends with a shared obsession for authentic streetwear culture decided to break the mold. Tired of mass-produced mediocrity, we set out to create something different – something real.
                  </p>
                  <p>
                    Every WOLLFOXX piece tells a story. From our signature oversized tees that redefine comfort to our meticulously crafted hoodies that blend urban aesthetics with premium quality, we don't just make clothes – we craft statements.
                  </p>
                  <p>
                    What started in a small studio has evolved into a movement. A community of individuals who refuse to blend in, who choose to stand out, and who understand that true style comes from authenticity, not trends.
                  </p>
                </div>
              </motion.div>
            </ParallaxSection>

            <ParallaxSection offset={-30}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#214FC3]/20 to-[#54AEE1]/20 rounded-3xl blur-xl transform rotate-6" />
                <img
                  src="https://images.unsplash.com/photo-1588117305388-c2631a279f82?q=80&w=800&auto=format&fit=crop"
                  alt="WOLLFOXX Studio"
                  className="relative w-full h-96 object-cover rounded-3xl shadow-2xl"
                />
              </motion.div>
            </ParallaxSection>
          </div>
        </div>
      </section>

      {/* Product Philosophy */}
      <section className="relative z-10 py-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-black mb-6 text-white">
              OUR <span className="text-[#214FC3]">PHILOSOPHY</span>
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: "Premium Quality",
                description: "Every thread, every stitch, every detail crafted with uncompromising quality standards.",
                icon: "🏆"
              },
              {
                title: "Oversized Perfection",
                description: "Mastering the art of oversized fit – comfort meets style in perfect harmony.",
                icon: "👕"
              },
              {
                title: "Authentic Design",
                description: "Original graphics and concepts that speak to the soul of street culture.",
                icon: "🎨"
              },
              {
                title: "Sustainable Future",
                description: "Responsible production methods because we care about the planet we call home.",
                icon: "🌱"
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group p-8 rounded-2xl bg-gradient-to-br from-gray-900/50 to-black/50 border border-[#214FC3]/20 hover:border-[#54AEE1]/40 transition-all duration-300 hover:transform hover:-translate-y-2"
              >
                <div className="text-4xl mb-4">{item.icon}</div>
                <h3 className="text-xl font-bold text-white mb-4">{item.title}</h3>
                <p className="text-gray-300">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Social Feed Section */}
      <section className="relative z-10 py-24 bg-gradient-to-r from-gray-900/50 to-black/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-black mb-6 text-white">
              JOIN THE <span className="text-[#214FC3]">CULTURE</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Follow our journey and become part of the WOLLFOXX family
            </p>
            <div className="flex justify-center gap-6">
              {[Instagram, Twitter].map((Icon, index) => (
                <motion.a
                  key={index}
                  href="#"
                  className="p-4 bg-gradient-to-r from-[#214FC3]/20 to-[#54AEE1]/20 rounded-full hover:from-[#214FC3]/40 hover:to-[#54AEE1]/40 transition-all duration-300"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon size={24} className="text-white" />
                </motion.a>
              ))}
            </div>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {socialFeed.map((image, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, rotate: 2 }}
                className="aspect-square rounded-xl overflow-hidden group cursor-pointer"
              >
                <img
                  src={image}
                  alt={`WOLLFOXX lifestyle ${index + 1}`}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-24">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-5xl md:text-7xl font-black mb-8 bg-gradient-to-r from-white via-[#54AEE1] to-[#14b8a6] bg-clip-text text-transparent">
              READY TO STAND OUT?
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Join thousands who've already discovered what it means to wear WOLLFOXX. Experience the difference that premium streetwear makes.
            </p>

            <motion.button
              className="group relative px-16 py-6 bg-gradient-to-r from-[#214FC3] to-[#54AEE1] rounded-full font-bold text-xl overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-[#214FC3]/25"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="relative z-10 flex items-center gap-4">
                SHOP THE COLLECTION
                <ArrowRight className="group-hover:translate-x-2 transition-transform" size={24} />
              </span>
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#54AEE1] to-[#14b8a6]"
                initial={{ x: "100%" }}
                whileHover={{ x: 0 }}
                transition={{ duration: 0.3 }}
              />
            </motion.button>
          </motion.div>
        </div>
      </section>
    </div>
  );
}