import { motion, AnimatePresence } from 'framer-motion';
import { Heart } from 'lucide-react';

export default function WishlistConfirmation({ show, isAdded }) {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="fixed bottom-6 right-6 z-50 bg-gray-900 border border-gray-800 shadow-xl rounded-lg p-4 flex items-center gap-3"
        >
          <div className="bg-gray-800 w-10 h-10 rounded-full flex items-center justify-center">
            <Heart
              size={20}
              className={isAdded ? 'fill-red-500 text-red-500' : 'text-gray-300'}
            />
          </div>
          <div>
            <p className="text-white font-medium">
              {isAdded ? 'Added to Wishlist' : 'Removed from Wishlist'}
            </p>
            <p className="text-gray-400 text-sm">
              {isAdded
                ? 'Item has been added to your wishlist'
                : 'Item has been removed from your wishlist'}
            </p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}