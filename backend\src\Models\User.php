<?php

namespace Wolffoxx\Models;

use Wolffoxx\Config\Database;

/**
 * User Model
 *
 * Handles user authentication, profile management,
 * and user-related database operations.
 */
class User extends BaseModel
{
    protected string $table = 'users';

    protected array $fillable = [
        'uuid',
        'email',
        'password_hash',
        'first_name',
        'last_name',
        'phone',
        'date_of_birth',
        'gender',
        'profile_image',
        'newsletter_subscribed',
        'marketing_emails',
        'sms_notifications',
        'email_verification_token',
        'password_reset_token',
        'password_reset_expires_at',
        'is_active',
        'is_admin',
        'auth_method' // 'email' or 'phone'
    ];

    protected array $hidden = [
        'password_hash',
        'email_verification_token',
        'password_reset_token',
        'login_attempts',
        'locked_until'
    ];

    protected array $casts = [
        'is_active' => 'boolean',
        'is_admin' => 'boolean',
        'newsletter_subscribed' => 'boolean',
        'marketing_emails' => 'boolean',
        'sms_notifications' => 'boolean',
        'login_attempts' => 'integer'
    ];

    /**
     * Generate a simple UUID-like string
     */
    private function generateUuid(): string
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Override create method to auto-generate UUID
     */
    public function create(array $data): ?array
    {
        // Auto-generate UUID if not provided
        if (!isset($data['uuid'])) {
            $data['uuid'] = $this->generateUuid();
        }

        // Hash password if provided
        if (isset($data['password'])) {
            $data['password_hash'] = $this->hashPassword($data['password']);
            unset($data['password']);
        }

        // Generate email verification token if email is provided
        if (isset($data['email']) && !empty($data['email']) && !isset($data['email_verification_token'])) {
            $data['email_verification_token'] = bin2hex(random_bytes(32));
        }

        // Set default values
        $data['is_active'] = $data['is_active'] ?? true;
        $data['is_admin'] = $data['is_admin'] ?? false;
        $data['newsletter_subscribed'] = $data['newsletter_subscribed'] ?? false;
        $data['marketing_emails'] = $data['marketing_emails'] ?? true;
        $data['sms_notifications'] = $data['sms_notifications'] ?? false;

        return parent::create($data);
    }

    /**
     * Create new user with hashed password (legacy method)
     */
    public function createUser(array $userData): ?array
    {
        try {
            $this->beginTransaction();

            $user = $this->create($userData);

            $this->commit();

            return $user;

        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?array
    {
        return $this->findBy('email', $email);
    }

    /**
     * Find user by phone number
     */
    public function findByPhone(string $phone): ?array
    {
        return $this->findBy('phone', $phone);
    }

    /**
     * Find user by UUID
     */
    public function findByUuid(string $uuid): ?array
    {
        return $this->findBy('uuid', $uuid);
    }

    /**
     * Find user by email verification token
     */
    public function findByEmailVerificationToken(string $token): ?array
    {
        return $this->findBy('email_verification_token', $token);
    }

    /**
     * Find user by password reset token
     */
    public function findByPasswordResetToken(string $token): ?array
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE password_reset_token = ?
                AND password_reset_expires_at > NOW()
                LIMIT 1";

        $statement = Database::execute($sql, [$token]);
        $result = $statement->fetch();

        return $result ? $this->processResult($result) : null;
    }

    /**
     * Verify user password
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Hash password
     */
    public function hashPassword(string $password): string
    {
        $cost = (int)($_ENV['BCRYPT_ROUNDS'] ?? 12);
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => $cost]);
    }

    /**
     * Update user password
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        try {
            $hashedPassword = $this->hashPassword($newPassword);

            $sql = "UPDATE {$this->table}
                    SET password_hash = ?,
                        password_reset_token = NULL,
                        password_reset_expires_at = NULL,
                        updated_at = NOW()
                    WHERE id = ?";

            $statement = Database::execute($sql, [$hashedPassword, $userId]);

            return $statement->rowCount() > 0;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Verify user email
     */
    public function verifyEmail(string $token): bool
    {
        try {
            $sql = "UPDATE {$this->table}
                    SET email_verified_at = NOW(),
                        email_verification_token = NULL,
                        updated_at = NOW()
                    WHERE email_verification_token = ?";

            $statement = Database::execute($sql, [$token]);

            $verified = $statement->rowCount() > 0;

            return $verified;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Set password reset token
     */
    public function setPasswordResetToken(int $userId): string
    {
        try {
            $token = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1 hour

            $sql = "UPDATE {$this->table}
                    SET password_reset_token = ?,
                        password_reset_expires_at = ?,
                        updated_at = NOW()
                    WHERE id = ?";

            Database::execute($sql, [$token, $expiresAt, $userId]);

            return $token;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(int $userId): void
    {
        try {
            $sql = "UPDATE {$this->table}
                    SET last_login_at = NOW(),
                        login_attempts = 0,
                        locked_until = NULL,
                        updated_at = NOW()
                    WHERE id = ?";

            Database::execute($sql, [$userId]);

        } catch (\Exception $e) {
            // Silent fail for last login update
        }
    }

    /**
     * Increment login attempts
     */
    public function incrementLoginAttempts(string $email): void
    {
        try {
            $maxAttempts = 5;
            $lockDuration = 900; // 15 minutes

            $sql = "UPDATE {$this->table}
                    SET login_attempts = login_attempts + 1,
                        locked_until = CASE
                            WHEN login_attempts + 1 >= ? THEN DATE_ADD(NOW(), INTERVAL ? SECOND)
                            ELSE locked_until
                        END,
                        updated_at = NOW()
                    WHERE email = ?";

            Database::execute($sql, [$maxAttempts, $lockDuration, $email]);

        } catch (\Exception $e) {
            // Silent fail for login attempt increment
        }
    }

    /**
     * Check if user is locked
     */
    public function isUserLocked(string $email): bool
    {
        try {
            $sql = "SELECT locked_until FROM {$this->table}
                    WHERE email = ? AND locked_until > NOW()";

            $statement = Database::execute($sql, [$email]);

            return $statement->fetch() !== false;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get user profile with additional data
     */
    public function getProfile(int $userId): ?array
    {
        try {
            $sql = "SELECT u.*,
                           COUNT(DISTINCT w.id) as wishlist_count,
                           COUNT(DISTINCT o.id) as outfit_count,
                           COUNT(DISTINCT r.id) as review_count
                    FROM {$this->table} u
                    LEFT JOIN wishlists w ON u.id = w.user_id
                    LEFT JOIN outfits o ON u.id = o.user_id
                    LEFT JOIN reviews r ON u.id = r.user_id
                    WHERE u.id = ?
                    GROUP BY u.id";

            $statement = Database::execute($sql, [$userId]);
            $result = $statement->fetch();

            return $result ? $this->processResult($result) : null;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Update user profile
     */
    public function updateProfile(int $userId, array $profileData): ?array
    {
        // Remove sensitive fields that shouldn't be updated via profile
        $restrictedFields = ['password_hash', 'email_verification_token', 'password_reset_token', 'is_admin'];
        foreach ($restrictedFields as $field) {
            unset($profileData[$field]);
        }

        return $this->update($userId, $profileData);
    }

    /**
     * Check if email exists (for registration validation)
     */
    public function emailExists(string $email): bool
    {
        try {
            $sql = "SELECT 1 FROM {$this->table} WHERE email = ? LIMIT 1";
            $statement = Database::execute($sql, [$email]);

            return $statement->fetch() !== false;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if phone exists (for registration validation)
     */
    public function phoneExists(string $phone): bool
    {
        try {
            $sql = "SELECT 1 FROM {$this->table} WHERE phone = ? LIMIT 1";
            $statement = Database::execute($sql, [$phone]);

            return $statement->fetch() !== false;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if user profile is complete
     */
    public function isProfileComplete(int $userId): bool
    {
        try {
            $user = $this->findById($userId);

            if (!$user) {
                return false;
            }

            // Check required fields: name and email
            $hasName = !empty($user['first_name']) || !empty($user['last_name']);
            $hasEmail = !empty($user['email']) && filter_var($user['email'], FILTER_VALIDATE_EMAIL);

            return $hasName && $hasEmail;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get profile completion status
     */
    public function getProfileCompletionStatus(int $userId): array
    {
        try {
            $user = $this->findById($userId);

            if (!$user) {
                return [
                    'is_complete' => false,
                    'missing_fields' => ['name', 'email'],
                    'user' => null
                ];
            }

            $missingFields = [];

            // Check name (either first_name or last_name should exist)
            if (empty($user['first_name']) && empty($user['last_name'])) {
                $missingFields[] = 'name';
            }

            // Check email
            if (empty($user['email']) || !filter_var($user['email'], FILTER_VALIDATE_EMAIL)) {
                $missingFields[] = 'email';
            }

            return [
                'is_complete' => empty($missingFields),
                'missing_fields' => $missingFields,
                'user' => [
                    'id' => $user['id'],
                    'phone' => $user['phone'],
                    'first_name' => $user['first_name'] ?? '',
                    'last_name' => $user['last_name'] ?? '',
                    'email' => $user['email'] ?? ''
                ]
            ];

        } catch (\Exception $e) {
            return [
                'is_complete' => false,
                'missing_fields' => ['name', 'email'],
                'user' => null
            ];
        }
    }

    /**
     * Complete user profile (for modal completion)
     */
    public function completeProfile(int $userId, string $name, string $email): bool
    {
        try {
            // Validate inputs
            $name = trim($name);
            if (strlen($name) < 2) {
                throw new \InvalidArgumentException('Name must be at least 2 characters');
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \InvalidArgumentException('Invalid email format');
            }

            // Check if email is already taken by another user
            $existingUser = $this->findByEmail($email);
            if ($existingUser && $existingUser['id'] !== $userId) {
                throw new \InvalidArgumentException('Email already exists');
            }

            // Split name into first and last name
            $nameParts = explode(' ', $name, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) ? $nameParts[1] : '';

            $sql = "UPDATE {$this->table} SET
                    first_name = ?,
                    last_name = ?,
                    email = ?,
                    profile_completed = 1,
                    profile_completed_at = NOW(),
                    updated_at = NOW()
                    WHERE id = ?";

            $statement = Database::execute($sql, [$firstName, $lastName, $email, $userId]);
            return $statement->rowCount() > 0;

        } catch (\Exception $e) {
            error_log('Complete user profile failed: ' . $e->getMessage());
            throw $e;
        }
    }
}
