<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Order;
use Wolffoxx\Models\Cart;
use Wolffoxx\Models\User;
use Wolffoxx\Utils\Response;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Services\EmailService;

/**
 * Order Controller
 * 
 * Handles order creation, management, and tracking
 */
class OrderController
{
    private Order $orderModel;
    private Cart $cartModel;
    private User $userModel;
    private EmailService $emailService;

    public function __construct()
    {
        $this->orderModel = new Order();
        $this->cartModel = new Cart();
        $this->userModel = new User();
        $this->emailService = new EmailService();
    }

    /**
     * Create order from cart
     * POST /api/v1/orders
     */
    public function create(): void
    {
        try {
            // Check authentication
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $user = $this->userModel->findById($userId);
            if (!$user) {
                Response::error('User not found', 404);
                return;
            }

            // Validate user profile completion before order creation
            $profileStatus = $this->userModel->getProfileCompletionStatus($user['id']);
            if (!$profileStatus['is_complete']) {
                Response::error('Profile incomplete. Please complete your profile before placing an order.', 400, [
                    'missing_fields' => $profileStatus['missing_fields'],
                    'requires_profile_completion' => true
                ]);
                return;
            }

            // Validate required user data for order
            if (empty($user['phone'])) {
                Response::error('Phone number verification required', 400, [
                    'requires_phone_verification' => true
                ]);
                return;
            }

            // Get request data
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            $required = ['shipping_address', 'billing_address', 'payment_method'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    Response::error("Missing required field: {$field}", 400);
                    return;
                }
            }

            // Get user's cart
            $cart = $this->cartModel->getUserCart($user['id']);
            if (!$cart || empty($cart['items'])) {
                Response::error('Cart is empty', 400);
                return;
            }

            // Create order
            $orderData = [
                'user_id' => $user['id'],
                'customer_email' => $user['email'],
                'customer_phone' => $user['phone'],
                'customer_name' => $input['customer_name'] ?? $user['name'] ?? '',
                'shipping_address' => $input['shipping_address'],
                'billing_address' => $input['billing_address'],
                'payment_method' => $input['payment_method'],
                'subtotal' => $cart['subtotal'],
                'tax_amount' => $cart['tax_amount'] ?? 0,
                'shipping_amount' => $input['shipping_amount'] ?? 0,
                'discount_amount' => $cart['discount_amount'] ?? 0,
                'total_amount' => $cart['total_amount'],
                'coupon_code' => $cart['coupon_code'] ?? null,
                'order_notes' => $input['order_notes'] ?? null
            ];

            $orderId = $this->orderModel->createOrder($orderData, $cart['items']);

            if ($orderId) {
                // Clear cart after successful order
                $this->cartModel->clearCart($user['id']);

                // Get created order details
                $order = $this->orderModel->getOrderById($orderId);

                // Send order confirmation email
                $this->sendOrderConfirmationEmail($order, $user, $cart['items']);

                Response::success([
                    'order_id' => $orderId,
                    'order_number' => $order['order_number'],
                    'total_amount' => $order['total_amount'],
                    'status' => $order['status'],
                    'message' => 'Order created successfully'
                ]);
            } else {
                Response::error('Failed to create order', 500);
            }

        } catch (\Exception $e) {
            error_log('Order creation failed: ' . $e->getMessage());
            Response::error('Order creation failed', 500);
        }
    }

    /**
     * Get user's orders
     * GET /api/v1/orders
     */
    public function index(): void
    {
        $this->getUserOrders();
    }

    /**
     * Get user's orders
     * GET /api/v1/orders
     */
    public function getUserOrders(): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 10);

            $orders = $this->orderModel->getUserOrders($userId, $page, $limit);

            Response::success([
                'orders' => $orders,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $this->orderModel->getUserOrdersCount($userId)
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Get user orders failed: ' . $e->getMessage());
            Response::error('Failed to get orders', 500);
        }
    }

    /**
     * Get single order details
     * GET /api/v1/orders/{id}
     */
    public function show(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $orderId = (int)$params['id'];
            $order = $this->orderModel->getOrderById($orderId);

            if (!$order) {
                Response::error('Order not found', 404);
                return;
            }

            // Check if order belongs to user
            if ($order['user_id'] !== $userId) {
                Response::error('Access denied', 403);
                return;
            }

            // Get order items
            $order['items'] = $this->orderModel->getOrderItems($orderId);

            Response::success($order);

        } catch (\Exception $e) {
            error_log('Get order details failed: ' . $e->getMessage());
            Response::error('Failed to get order details', 500);
        }
    }

    /**
     * Update order status
     * PUT /api/v1/orders/{id}/status
     */
    public function updateStatus(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $orderId = (int)$params['id'];
            $input = json_decode(file_get_contents('php://input'), true);

            if (empty($input['status'])) {
                Response::error('Status is required', 400);
                return;
            }

            $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
            if (!in_array($input['status'], $validStatuses)) {
                Response::error('Invalid status', 400);
                return;
            }

            // Check if order exists and belongs to user
            $order = $this->orderModel->getOrderById($orderId);
            if (!$order || $order['user_id'] !== $userId) {
                Response::error('Order not found', 404);
                return;
            }

            // Only allow cancellation by user
            if ($input['status'] !== 'cancelled') {
                Response::error('You can only cancel orders', 403);
                return;
            }

            // Don't allow cancellation of shipped/delivered orders
            if (in_array($order['status'], ['shipped', 'delivered'])) {
                Response::error('Cannot cancel shipped or delivered orders', 400);
                return;
            }

            $success = $this->orderModel->updateOrderStatus($orderId, $input['status']);

            if ($success) {
                Response::success(['message' => 'Order status updated successfully']);
            } else {
                Response::error('Failed to update order status', 500);
            }

        } catch (\Exception $e) {
            error_log('Update order status failed: ' . $e->getMessage());
            Response::error('Failed to update order status', 500);
        }
    }

    /**
     * Track order
     * GET /api/v1/orders/{id}/track
     */
    public function trackOrder(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $orderId = (int)$params['id'];
            $order = $this->orderModel->getOrderById($orderId);

            if (!$order || $order['user_id'] !== $userId) {
                Response::error('Order not found', 404);
                return;
            }

            $tracking = [
                'order_number' => $order['order_number'],
                'status' => $order['status'],
                'tracking_number' => $order['tracking_number'],
                'estimated_delivery_date' => $order['estimated_delivery_date'],
                'delivered_at' => $order['delivered_at'],
                'status_history' => $this->orderModel->getOrderStatusHistory($orderId)
            ];

            Response::success($tracking);

        } catch (\Exception $e) {
            error_log('Track order failed: ' . $e->getMessage());
            Response::error('Failed to track order', 500);
        }
    }

    /**
     * Send order confirmation email
     */
    private function sendOrderConfirmationEmail(array $order, array $user, array $orderItems): void
    {
        try {
            // Only send email if user has an email address
            $email = $user['email'] ?? $order['customer_email'] ?? null;

            if (empty($email)) {
                error_log("No email address found for order {$order['order_number']}");
                return;
            }

            // Send order confirmation email
            $emailSent = $this->emailService->send(
                $email,
                "Order Confirmation - {$order['order_number']}",
                'order-confirmation',
                [
                    'order' => $order,
                    'user' => $user,
                    'orderItems' => $orderItems
                ]
            );

            if ($emailSent) {
                error_log("Order confirmation email sent successfully for order {$order['order_number']} to {$email}");
            } else {
                error_log("Failed to send order confirmation email for order {$order['order_number']} to {$email}");
            }

        } catch (\Exception $e) {
            error_log("Order confirmation email error for order {$order['order_number']}: " . $e->getMessage());
        }
    }
}
