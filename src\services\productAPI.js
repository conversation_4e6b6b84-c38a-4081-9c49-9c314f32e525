// Product API Service
const API_BASE_URL = 'http://localhost:8000/api/v1';

// API Error Class
class APIError extends Error {
  constructor(message, status, data = null) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.data = data;
  }
}

// HTTP Client for Product API
const httpClient = {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      // Parse response
      let data;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      // Handle HTTP errors
      if (!response.ok) {
        const errorMessage = data?.message || data?.error || `HTTP ${response.status}: ${response.statusText}`;
        throw new APIError(errorMessage, response.status, data);
      }

      // Return data from successful response
      return data?.data || data;

    } catch (error) {
      // Network or parsing errors
      if (error instanceof APIError) {
        throw error;
      }

      // Network error
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new APIError('Network error. Please check your connection.', 0);
      }

      // Other errors
      throw new APIError(error.message || 'An unexpected error occurred', 0);
    }
  },

  get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options });
  }
};

// Product API Service
export const productAPI = {
  /**
   * Get all products with filtering and pagination
   */
  async getProducts(filters = {}, page = 1, perPage = 20) {
    try {
      const queryParams = new URLSearchParams();
      
      // Add filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          queryParams.append(key, value);
        }
      });
      
      // Add pagination
      queryParams.append('page', page);
      queryParams.append('per_page', perPage);

      const endpoint = `/products?${queryParams.toString()}`;
      const response = await httpClient.get(endpoint);

      return {
        products: response.data || response,
        pagination: response.pagination || {},
        total: response.total || 0
      };
    } catch (error) {
      console.error('Get products error:', error);
      throw error;
    }
  },

  /**
   * Get single product by ID
   */
  async getProduct(productId) {
    try {
      if (!productId) {
        throw new APIError('Product ID is required');
      }

      const response = await httpClient.get(`/products/${productId}`);
      return response;
    } catch (error) {
      console.error('Get product error:', error);
      throw error;
    }
  },

  /**
   * Get products by category
   */
  async getProductsByCategory(category, filters = {}, page = 1, perPage = 20) {
    try {
      if (!category) {
        throw new APIError('Category is required');
      }

      const queryParams = new URLSearchParams();
      
      // Add filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          queryParams.append(key, value);
        }
      });
      
      // Add pagination
      queryParams.append('page', page);
      queryParams.append('per_page', perPage);

      const endpoint = `/products/category/${encodeURIComponent(category)}?${queryParams.toString()}`;
      const response = await httpClient.get(endpoint);

      return {
        products: response.data || response,
        pagination: response.pagination || {},
        total: response.total || 0,
        category: category
      };
    } catch (error) {
      console.error('Get products by category error:', error);
      throw error;
    }
  },

  /**
   * Search products
   */
  async searchProducts(query, filters = {}, page = 1, perPage = 20) {
    try {
      if (!query || query.trim() === '') {
        throw new APIError('Search query is required');
      }

      const queryParams = new URLSearchParams();
      queryParams.append('q', query.trim());
      
      // Add filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          queryParams.append(key, value);
        }
      });
      
      // Add pagination
      queryParams.append('page', page);
      queryParams.append('per_page', perPage);

      const endpoint = `/products/search?${queryParams.toString()}`;
      const response = await httpClient.get(endpoint);

      return {
        products: response.data || response,
        pagination: response.pagination || {},
        total: response.total || 0,
        query: query
      };
    } catch (error) {
      console.error('Search products error:', error);
      throw error;
    }
  },

  /**
   * Get all categories
   */
  async getCategories() {
    try {
      const response = await httpClient.get('/categories');
      return response;
    } catch (error) {
      console.error('Get categories error:', error);
      throw error;
    }
  },

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit = 12) {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('limit', limit);

      const endpoint = `/products?featured=true&${queryParams.toString()}`;
      const response = await httpClient.get(endpoint);

      return response.data || response;
    } catch (error) {
      console.error('Get featured products error:', error);
      throw error;
    }
  },

  /**
   * Get new products
   */
  async getNewProducts(limit = 12) {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('limit', limit);

      const endpoint = `/products?new=true&${queryParams.toString()}`;
      const response = await httpClient.get(endpoint);

      return response.data || response;
    } catch (error) {
      console.error('Get new products error:', error);
      throw error;
    }
  },

  /**
   * Get bestseller products
   */
  async getBestsellerProducts(limit = 12) {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('limit', limit);

      const endpoint = `/products?bestseller=true&${queryParams.toString()}`;
      const response = await httpClient.get(endpoint);

      return response.data || response;
    } catch (error) {
      console.error('Get bestseller products error:', error);
      throw error;
    }
  },

  /**
   * Get sale products
   */
  async getSaleProducts(filters = {}, page = 1, perPage = 20) {
    try {
      const queryParams = new URLSearchParams();
      
      // Add filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          queryParams.append(key, value);
        }
      });
      
      // Add pagination
      queryParams.append('page', page);
      queryParams.append('per_page', perPage);

      const endpoint = `/products?on_sale=true&${queryParams.toString()}`;
      const response = await httpClient.get(endpoint);

      return {
        products: response.data || response,
        pagination: response.pagination || {},
        total: response.total || 0
      };
    } catch (error) {
      console.error('Get sale products error:', error);
      throw error;
    }
  }
};

// Export utilities
export { APIError };
