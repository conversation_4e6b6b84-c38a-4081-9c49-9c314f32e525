<?php

namespace Wolffoxx\Models;

use Wolffoxx\Config\Database;
use PDOException;

/**
 * Base Model Class
 *
 * Provides common database operations and utilities
 * for all model classes.
 */
abstract class BaseModel
{
    protected string $table;
    protected string $primaryKey = 'id';
    protected array $fillable = [];
    protected array $hidden = [];
    protected array $casts = [];
    public function __construct()
    {
        // BaseModel initialized without logger for simplicity
    }

    /**
     * Find record by ID
     */
    public function findById(int $id): ?array
    {
        try {
            $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ? LIMIT 1";
            $statement = Database::execute($sql, [$id]);
            $result = $statement->fetch();

            if ($result) {
                return $this->processResult($result);
            }

            return null;

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Find record by field
     */
    public function findBy(string $field, $value): ?array
    {
        try {
            $sql = "SELECT * FROM {$this->table} WHERE {$field} = ? LIMIT 1";
            $statement = Database::execute($sql, [$value]);
            $result = $statement->fetch();

            if ($result) {
                return $this->processResult($result);
            }

            return null;

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Get all records with optional conditions
     */
    public function findAll(array $conditions = [], array $orderBy = [], int $limit = null, int $offset = null): array
    {
        try {
            $sql = "SELECT * FROM {$this->table}";
            $params = [];

            // Add WHERE conditions
            if (!empty($conditions)) {
                $whereClause = [];
                foreach ($conditions as $field => $value) {
                    $whereClause[] = "{$field} = ?";
                    $params[] = $value;
                }
                $sql .= " WHERE " . implode(' AND ', $whereClause);
            }

            // Add ORDER BY
            if (!empty($orderBy)) {
                $orderClause = [];
                foreach ($orderBy as $field => $direction) {
                    $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';
                    $orderClause[] = "{$field} {$direction}";
                }
                $sql .= " ORDER BY " . implode(', ', $orderClause);
            }

            // Add LIMIT and OFFSET
            if ($limit !== null) {
                $sql .= " LIMIT {$limit}";
                if ($offset !== null) {
                    $sql .= " OFFSET {$offset}";
                }
            }

            $statement = Database::execute($sql, $params);
            $results = $statement->fetchAll();

            return array_map([$this, 'processResult'], $results);

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Create new record
     */
    public function create(array $data): ?array
    {
        try {
            // Filter data to only include fillable fields
            $filteredData = $this->filterFillable($data);

            if (empty($filteredData)) {
                throw new \InvalidArgumentException('No valid data provided for creation');
            }

            $fields = array_keys($filteredData);
            $placeholders = array_fill(0, count($fields), '?');

            $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";

            Database::execute($sql, array_values($filteredData));

            $id = Database::lastInsertId();

            return $this->findById((int)$id);

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Update record by ID
     */
    public function update(int $id, array $data): ?array
    {
        try {
            // Filter data to only include fillable fields
            $filteredData = $this->filterFillable($data);

            if (empty($filteredData)) {
                throw new \InvalidArgumentException('No valid data provided for update');
            }

            $fields = array_keys($filteredData);
            $setClause = array_map(fn($field) => "{$field} = ?", $fields);

            $sql = "UPDATE {$this->table} SET " . implode(', ', $setClause) . " WHERE {$this->primaryKey} = ?";

            $params = array_values($filteredData);
            $params[] = $id;

            $statement = Database::execute($sql, $params);

            return $this->findById($id);

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Delete record by ID
     */
    public function delete(int $id): bool
    {
        try {
            $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
            $statement = Database::execute($sql, [$id]);

            $deleted = $statement->rowCount() > 0;

            return $deleted;

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Count records with optional conditions
     */
    public function count(array $conditions = []): int
    {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$this->table}";
            $params = [];

            if (!empty($conditions)) {
                $whereClause = [];
                foreach ($conditions as $field => $value) {
                    $whereClause[] = "{$field} = ?";
                    $params[] = $value;
                }
                $sql .= " WHERE " . implode(' AND ', $whereClause);
            }

            $statement = Database::execute($sql, $params);
            $result = $statement->fetch();

            return (int)$result['count'];

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Check if record exists
     */
    public function exists(int $id): bool
    {
        try {
            $sql = "SELECT 1 FROM {$this->table} WHERE {$this->primaryKey} = ? LIMIT 1";
            $statement = Database::execute($sql, [$id]);

            return $statement->fetch() !== false;

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Filter data to only include fillable fields
     */
    protected function filterFillable(array $data): array
    {
        if (empty($this->fillable)) {
            return $data;
        }

        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * Process database result (apply casts, hide fields, etc.)
     */
    protected function processResult(array $result): array
    {
        // Apply casts
        foreach ($this->casts as $field => $type) {
            if (isset($result[$field])) {
                $result[$field] = $this->castValue($result[$field], $type);
            }
        }

        // Hide sensitive fields
        foreach ($this->hidden as $field) {
            unset($result[$field]);
        }

        return $result;
    }

    /**
     * Cast value to specified type
     */
    protected function castValue($value, string $type)
    {
        if ($value === null) {
            return null;
        }

        return match ($type) {
            'int', 'integer' => (int)$value,
            'float', 'double' => (float)$value,
            'bool', 'boolean' => (bool)$value,
            'string' => (string)$value,
            'array', 'json' => json_decode($value, true),
            'datetime' => new \DateTime($value),
            default => $value
        };
    }

    /**
     * Begin database transaction
     */
    protected function beginTransaction(): void
    {
        Database::beginTransaction();
    }

    /**
     * Commit database transaction
     */
    protected function commit(): void
    {
        Database::commit();
    }

    /**
     * Rollback database transaction
     */
    protected function rollback(): void
    {
        Database::rollback();
    }
}
