<?php

namespace Wolffoxx\Models;

use Wolffoxx\Models\Database;

/**
 * Order Model
 * 
 * Handles order data operations
 */
class Order
{
    /**
     * Create new order with items
     */
    public function createOrder(array $orderData, array $cartItems): ?int
    {
        try {
            Database::beginTransaction();

            // Generate unique order number
            $orderNumber = $this->generateOrderNumber();
            $orderData['order_number'] = $orderNumber;
            $orderData['uuid'] = $this->generateUUID();

            // Insert order
            $sql = "INSERT INTO orders (
                uuid, user_id, order_number, customer_email, customer_phone, customer_name,
                shipping_address_line1, shipping_address_line2, shipping_city, shipping_state, 
                shipping_postal_code, shipping_country,
                billing_address_line1, billing_address_line2, billing_city, billing_state,
                billing_postal_code, billing_country,
                payment_method, subtotal, tax_amount, shipping_amount, discount_amount, 
                total_amount, coupon_code, order_notes, status, payment_status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending')";

            $shippingAddress = $orderData['shipping_address'];
            $billingAddress = $orderData['billing_address'];

            $params = [
                $orderData['uuid'],
                $orderData['user_id'],
                $orderData['order_number'],
                $orderData['customer_email'],
                $orderData['customer_phone'],
                $orderData['customer_name'],
                $shippingAddress['line1'],
                $shippingAddress['line2'] ?? null,
                $shippingAddress['city'],
                $shippingAddress['state'],
                $shippingAddress['postal_code'],
                $shippingAddress['country'] ?? 'India',
                $billingAddress['line1'],
                $billingAddress['line2'] ?? null,
                $billingAddress['city'],
                $billingAddress['state'],
                $billingAddress['postal_code'],
                $billingAddress['country'] ?? 'India',
                $orderData['payment_method'],
                $orderData['subtotal'],
                $orderData['tax_amount'],
                $orderData['shipping_amount'],
                $orderData['discount_amount'],
                $orderData['total_amount'],
                $orderData['coupon_code'],
                $orderData['order_notes']
            ];

            $statement = Database::execute($sql, $params);
            $orderId = Database::getLastInsertId();

            // Insert order items
            foreach ($cartItems as $item) {
                $this->addOrderItem($orderId, $item);
            }

            Database::commit();
            return $orderId;

        } catch (\Exception $e) {
            Database::rollback();
            error_log('Create order failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add item to order
     */
    private function addOrderItem(int $orderId, array $item): void
    {
        $sql = "INSERT INTO order_items (
            order_id, product_id, selected_color, selected_size, selected_color_hex,
            quantity, unit_price, sale_price, total_price, product_name, product_image,
            product_sku, product_category, outfit_id, outfit_name
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $orderId,
            $item['product_id'],
            $item['selected_color'] ?? null,
            $item['selected_size'] ?? null,
            $item['selected_color_hex'] ?? null,
            $item['quantity'],
            $item['unit_price'],
            $item['sale_price'] ?? null,
            $item['total_price'],
            $item['product_name'],
            $item['product_image'] ?? null,
            $item['product_sku'] ?? null,
            $item['product_category'] ?? null,
            $item['outfit_id'] ?? null,
            $item['outfit_name'] ?? null
        ];

        Database::execute($sql, $params);
    }

    /**
     * Get order by ID
     */
    public function getOrderById(int $orderId): ?array
    {
        $sql = "SELECT * FROM orders WHERE id = ?";
        $statement = Database::execute($sql, [$orderId]);
        $order = $statement->fetch();

        return $order ?: null;
    }

    /**
     * Get user's orders
     */
    public function getUserOrders(int $userId, int $page = 1, int $limit = 10): array
    {
        $offset = ($page - 1) * $limit;

        $sql = "SELECT 
                    id, uuid, order_number, status, payment_status, total_amount,
                    created_at, estimated_delivery_date, delivered_at
                FROM orders 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?";

        $statement = Database::execute($sql, [$userId, $limit, $offset]);
        return $statement->fetchAll();
    }

    /**
     * Get user's orders count
     */
    public function getUserOrdersCount(int $userId): int
    {
        $sql = "SELECT COUNT(*) as count FROM orders WHERE user_id = ?";
        $statement = Database::execute($sql, [$userId]);
        $result = $statement->fetch();
        return (int)$result['count'];
    }

    /**
     * Get order items
     */
    public function getOrderItems(int $orderId): array
    {
        $sql = "SELECT * FROM order_items WHERE order_id = ? ORDER BY id";
        $statement = Database::execute($sql, [$orderId]);
        return $statement->fetchAll();
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(int $orderId, string $status): bool
    {
        $sql = "UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?";
        $statement = Database::execute($sql, [$status, $orderId]);
        return $statement->rowCount() > 0;
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(int $orderId, string $paymentStatus, array $paymentData = []): bool
    {
        $sql = "UPDATE orders SET 
                payment_status = ?, 
                payment_gateway_payment_id = ?,
                payment_gateway_signature = ?,
                updated_at = NOW() 
                WHERE id = ?";

        $params = [
            $paymentStatus,
            $paymentData['payment_id'] ?? null,
            $paymentData['signature'] ?? null,
            $orderId
        ];

        $statement = Database::execute($sql, $params);
        return $statement->rowCount() > 0;
    }

    /**
     * Get order status history (simplified - you can expand this)
     */
    public function getOrderStatusHistory(int $orderId): array
    {
        // For now, return current status
        // You can create a separate order_status_history table for detailed tracking
        $order = $this->getOrderById($orderId);
        
        if (!$order) {
            return [];
        }

        return [
            [
                'status' => 'pending',
                'timestamp' => $order['created_at'],
                'description' => 'Order placed successfully'
            ],
            [
                'status' => $order['status'],
                'timestamp' => $order['updated_at'],
                'description' => $this->getStatusDescription($order['status'])
            ]
        ];
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber(): string
    {
        $prefix = 'WF';
        $timestamp = date('ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }

    /**
     * Generate UUID
     */
    private function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Get status description
     */
    private function getStatusDescription(string $status): string
    {
        $descriptions = [
            'pending' => 'Order is being processed',
            'confirmed' => 'Order confirmed and being prepared',
            'processing' => 'Order is being processed',
            'shipped' => 'Order has been shipped',
            'delivered' => 'Order delivered successfully',
            'cancelled' => 'Order has been cancelled'
        ];

        return $descriptions[$status] ?? 'Status updated';
    }
}
