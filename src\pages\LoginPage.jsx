import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { X } from 'lucide-react';
import OTPLogin from '../components/auth/OTPLogin';
import { useAuth } from '../context/AuthContext';
import drawingLogo from '../assets/logo45.svg';

const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // Handle successful login
  const handleLoginSuccess = (data) => {
    const from = location.state?.from?.pathname || '/';
    
    // Show success message briefly
    setTimeout(() => {
      navigate(from, { replace: true });
    }, 500);
  };

  // Handle close/back
  const handleClose = () => {
    navigate(-1);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 120,
        damping: 20
      }
    }
  };

  const floatingVariants = {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-950 to-black relative overflow-hidden">
      {/* Advanced Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        {/* Primary gradient orb */}
        <motion.div 
          className="absolute -top-60 -right-60 w-[600px] h-[600px] bg-gradient-to-br from-orange-500/8 via-orange-600/4 to-transparent rounded-full blur-3xl"
          animate={{
            scale: [1, 1.1, 0.9, 1],
            opacity: [0.3, 0.5, 0.2, 0.3],
            rotate: [0, 90, 180, 360]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Secondary gradient orb */}
        <motion.div 
          className="absolute -bottom-60 -left-60 w-[500px] h-[500px] bg-gradient-to-tr from-gray-600/6 via-gray-500/3 to-transparent rounded-full blur-3xl"
          animate={{
            scale: [0.8, 1.2, 1, 0.8],
            opacity: [0.2, 0.4, 0.1, 0.2],
            rotate: [360, 270, 180, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Accent orb */}
        <motion.div 
          className="absolute top-1/2 left-1/4 w-96 h-96 bg-gradient-to-r from-orange-400/3 to-gray-400/2 rounded-full blur-2xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.3, 0.1],
            x: [0, 50, 0],
            y: [0, -30, 0]
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Subtle grid pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.01)_1px,transparent_1px)] [background-size:50px_50px]" />
        
        {/* Noise texture overlay */}
        <div
          className="absolute inset-0"
          style={{
            opacity: 0.015,
            backgroundImage:
              "url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\"%3E%3Ccircle cx=\"7\" cy=\"7\" r=\"1\"/%3E%3Ccircle cx=\"27\" cy=\"7\" r=\"1\"/%3E%3Ccircle cx=\"47\" cy=\"7\" r=\"1\"/%3E%3Ccircle cx=\"7\" cy=\"27\" r=\"1\"/%3E%3Ccircle cx=\"27\" cy=\"27\" r=\"1\"/%3E%3Ccircle cx=\"47\" cy=\"27\" r=\"1\"/%3E%3Ccircle cx=\"7\" cy=\"47\" r=\"1\"/%3E%3Ccircle cx=\"27\" cy=\"47\" r=\"1\"/%3E%3Ccircle cx=\"47\" cy=\"47\" r=\"1\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"
          }}
        />
      </div>

      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-20 flex items-center justify-between p-6 md:p-8 border-b border-gray-800/20 backdrop-blur-sm"
      >
        <div className="flex items-center gap-6">
          <motion.div
            className="hidden sm:block"
            variants={floatingVariants}
            animate="animate"
          >
            <motion.h1 
              className="text-white font-bold text-2xl bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              Welcome Back
            </motion.h1>
            <motion.p 
              className="text-gray-400 text-sm mt-2 font-medium"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              Sign in to continue your journey
            </motion.p>
          </motion.div>
        </div>
        
        <motion.button
          onClick={handleClose}
          className="relative p-4 text-gray-500 hover:text-white transition-all duration-500 rounded-2xl hover:bg-gradient-to-r hover:from-gray-800/60 hover:to-gray-700/40 group backdrop-blur-sm border border-gray-800/30 hover:border-gray-600/50"
          whileHover={{ 
            scale: 1.05,
            rotate: 5
          }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, rotate: -90 }}
          animate={{ opacity: 1, rotate: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <X size={20} className="group-hover:rotate-90 transition-transform duration-500" />
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-500/0 to-orange-400/0 group-hover:from-orange-500/10 group-hover:to-orange-400/5 transition-all duration-500" />
        </motion.button>
      </motion.header>

      {/* Main Content */}
      <div className="relative z-10 flex-1 flex items-center justify-center p-6 md:p-8 min-h-[calc(100vh-120px)]">
        <motion.div 
          className="w-full max-w-lg"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Login Form Container */}
          <motion.div
            variants={itemVariants}
            className="relative group"
          >
            {/* Glow effect */}
            <div className="absolute -inset-1 bg-gradient-to-r from-orange-500/20 via-gray-500/10 to-orange-400/20 rounded-3xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-1000" />
            
            {/* Main form card */}
            <div className="relative bg-gradient-to-br from-gray-900/80 via-gray-950/60 to-black/80 backdrop-blur-2xl border border-gray-700/40 rounded-3xl p-10 md:p-12 shadow-2xl shadow-black/40">
              {/* Inner glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/[0.02] via-transparent to-gray-500/[0.02] rounded-3xl" />
              
              {/* Content */}
              <div className="relative z-10">
                <OTPLogin
                  onSuccess={handleLoginSuccess}
                  onClose={handleClose}
                />
              </div>

              {/* Decorative elements */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full opacity-60" />
              <div className="absolute bottom-4 left-4 w-1 h-1 bg-gradient-to-r from-gray-400 to-gray-500 rounded-full opacity-40" />
            </div>
          </motion.div>

          {/* Enhanced Terms Section */}
          <motion.div
            variants={itemVariants}
            className="mt-10 text-center"
          >
            <div className="relative group">
              {/* Background glow */}
              <div className="absolute -inset-2 bg-gradient-to-r from-gray-500/5 via-orange-500/5 to-gray-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
              
              {/* Main container */}
              <div className="relative p-6 bg-gradient-to-br from-gray-900/40 via-gray-950/30 to-black/40 rounded-2xl border border-gray-700/30 backdrop-blur-xl">
                <p className="text-gray-400 text-sm leading-relaxed font-medium">
                  By continuing, you agree to our{' '}
                  <motion.button 
                    className="relative text-orange-400 hover:text-orange-300 font-semibold group/link"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="relative z-10">Terms of Service</span>
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-orange-400 to-orange-500 scale-x-0 group-hover/link:scale-x-100 transition-transform duration-300 origin-left" />
                  </motion.button>{' '}
                  and{' '}
                  <motion.button 
                    className="relative text-orange-400 hover:text-orange-300 font-semibold group/link"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="relative z-10">Privacy Policy</span>
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-orange-400 to-orange-500 scale-x-0 group-hover/link:scale-x-100 transition-transform duration-300 origin-left" />
                  </motion.button>
                </p>

                {/* Decorative line */}
                <div className="mt-4 h-px bg-gradient-to-r from-transparent via-gray-600/30 to-transparent" />
              </div>
            </div>
          </motion.div>

          {/* Floating decorative elements */}
          <motion.div
            className="absolute -top-20 -right-20 w-32 h-32 border border-gray-700/20 rounded-full"
            animate={{
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          
          <motion.div
            className="absolute -bottom-16 -left-16 w-24 h-24 border border-orange-500/10 rounded-full"
            animate={{
              rotate: [360, 0],
              scale: [1, 0.9, 1]
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </motion.div>
      </div>

      {/* Bottom ambient light */}
      <div className="fixed bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-orange-500/[0.02] to-transparent pointer-events-none" />
    </div>
  );
};

export default LoginPage;