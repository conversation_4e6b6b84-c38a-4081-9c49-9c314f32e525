import { useState } from 'react';
import { Heart } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useWishlist } from '../context/WishlistContext';
import WishlistConfirmation from './WishlistConfirmation';

export default function WishlistButton({
  productId,
  productName,
  productPrice,
  productImage,
  className = ''
}) {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const [animate, setAnimate] = useState(false);
  const [lastAction, setLastAction] = useState(null); // Track the last action performed

  const isActive = isInWishlist(productId);

  const handleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const willBeAdded = !isActive; // Store the action we're about to perform
    setLastAction(willBeAdded ? 'added' : 'removed');
    setAnimate(true);

    if (isActive) {
      // Remove from wishlist if already in wishlist
      removeFromWishlist(productId);
    } else {
      // Add to wishlist if not in wishlist
      addToWishlist({
        id: productId,
        name: productName,
        price: productPrice,
        image: productImage,
      });
    }

    // Reset animation state
    setTimeout(() => {
      setAnimate(false);
      setLastAction(null);
    }, 1000);
  };

  return (
    <>
      <button
        className={`relative ${className}`}
        onClick={handleClick}
        aria-label={isActive ? "Remove from wishlist" : "Add to wishlist"}
      >
        <Heart
          size={14}
          className={`transition-colors duration-300 ${
            isActive ? 'fill-red-500 text-red-500' : 'text-gray-300 hover:text-white'
          }`}
        />

        <AnimatePresence>
          {animate && (
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1.5, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="absolute inset-0 flex items-center justify-center pointer-events-none"
            >
              <Heart size={20} className="text-red-500 fill-red-500" />
            </motion.div>
          )}
        </AnimatePresence>
      </button>

      {/* Wishlist confirmation toast */}
      {animate && <WishlistConfirmation show={animate} isAdded={lastAction === 'added'} />}
    </>
  );
}