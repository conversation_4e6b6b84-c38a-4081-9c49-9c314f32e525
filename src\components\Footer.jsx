import { Instagram, Twitter, CreditCard } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-[#1a1a1a] backdrop-blur-sm pt-12 pb-6 border-t border-[#2a2a2a]">
      <div className="container mx-auto px-4 md:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-6">
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-3xl font-['Bebas_Neue',sans-serif] text-[#f5f5f5] mb-4 tracking-wider">@WOLFFOXX</h3>
            <p className="text-[#9a9a9a] max-w-md mb-6 leading-relaxed">
              Elevating streetwear with premium quality oversized pieces. Join our community of trendsetters.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="group bg-[#2a2a2a] p-2 rounded-lg border border-[#404040] text-[#9a9a9a] hover:text-[#d4d4d4] hover:border-[#6a6a6a] transition-all duration-300">
                <Instagram size={20} className="group-hover:scale-110 transition-transform" />
              </a>
              <a href="#" className="group bg-[#2a2a2a] p-2 rounded-lg border border-[#404040] text-[#9a9a9a] hover:text-[#d4d4d4] hover:border-[#6a6a6a] transition-all duration-300">
                <Twitter size={20} className="group-hover:scale-110 transition-transform" />
              </a>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-white mb-4 text-lg">Shop</h4>
            <ul className="space-y-3">
              <li>
                <a href="/collection" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                  All Products
                </a>
              </li>
              <li>
                <a href="/new-arrivals" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                  New Arrivals
                </a>
              </li>
              <li>
                <a href="/best-sellers" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                  Best Sellers
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold text-white mb-4 text-lg">Support</h4>
            <ul className="space-y-3">
              <li>
                <a href="/faq" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                  FAQs
                </a>
              </li>
              <li>
                <a href="/shipping" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                  Shipping Info
                </a>
              </li>
              <li>
                <a href="/returns" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                  Returns
                </a>
              </li>
              <li>
                <a href="/size-guide" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                  Size Guide
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Legal Section - Mobile Responsive */}
        <div className="mt-8 md:hidden">
          <h4 className="font-semibold text-white mb-4 text-lg">Legal</h4>
          <ul className="space-y-3 flex flex-col sm:flex-row sm:space-y-0 sm:space-x-6">
            <li>
              <a href="/privacy" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                Privacy Policy
              </a>
            </li>
            <li>
              <a href="/terms" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block">
                Terms & Conditions
              </a>
            </li>
          </ul>
        </div>

        <div className="border-t border-slate-600/50 pt-6 mt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6">
              <p className="text-slate-300 text-sm">
                © {new Date().getFullYear()} WOLFFOXX. All rights reserved.
              </p>
              {/* Legal links for desktop */}
              <div className="hidden md:flex items-center gap-4 text-sm">
                <a href="/privacy" className="text-slate-400 hover:text-slate-300 transition-colors">
                  Privacy Policy
                </a>
                <span className="text-slate-600">•</span>
                <a href="/terms" className="text-slate-400 hover:text-slate-300 transition-colors">
                  Terms & Conditions
                </a>
              </div>
            </div>

            <div className="flex items-center gap-2 flex-wrap justify-center lg:justify-end">
              <div className="flex items-center gap-2 bg-slate-800/50 px-3 py-2 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-colors">
                <CreditCard size={16} className="text-slate-300" />
                <span className="text-slate-300 text-xs font-medium">VISA</span>
              </div>
              <div className="flex items-center gap-2 bg-slate-800/50 px-3 py-2 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-colors">
                <CreditCard size={16} className="text-slate-300" />
                <span className="text-slate-300 text-xs font-medium">MASTERCARD</span>
              </div>
              <div className="flex items-center gap-2 bg-slate-800/50 px-3 py-2 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-colors">
                <CreditCard size={16} className="text-slate-300" />
                <span className="text-slate-300 text-xs font-medium">AMEX</span>
              </div>
              <div className="flex items-center gap-2 bg-slate-800/50 px-3 py-2 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-colors">
                <CreditCard size={16} className="text-slate-300" />
                <span className="text-slate-300 text-xs font-medium">PAYPAL</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}