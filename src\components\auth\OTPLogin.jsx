import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, ArrowRight, RotateCcw, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { validatePhone, validateOTP } from '../../services/authAPI';

const OTPLogin = ({ onSuccess, onClose }) => {
  const [step, setStep] = useState('phone'); // 'phone' | 'otp' | 'profile'
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [userDetails, setUserDetails] = useState({
    first_name: '',
    last_name: '',
    email: ''
  });
  const [resendTimer, setResendTimer] = useState(0);
  const [errors, setErrors] = useState({});

  const { sendOTP, verifyOTP, resendOTP, isLoading, error, otpSent, originalPhone, clearError } = useAuth();

  // Auto-advance to OTP step when OTP is sent
  useEffect(() => {
    if (otpSent && step === 'phone') {
      console.log('Advancing to OTP step, current phone state:', phone);
      setStep('otp');
      setResendTimer(60);
    }
  }, [otpSent, step, phone]);

  const otpRefs = useRef([]);
  const phoneInputRef = useRef(null);

  // Timer for resend OTP
  useEffect(() => {
    let interval;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Focus phone input on mount
  useEffect(() => {
    if (phoneInputRef.current) {
      phoneInputRef.current.focus();
    }
  }, []);

  // Clear errors when inputs change
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => clearError(), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Format phone number display
  const formatPhoneDisplay = (value) => {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length <= 10) {
      return cleaned.replace(/(\d{5})(\d{5})/, '$1 $2');
    }
    return cleaned;
  };

  // Handle phone input
  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, '');
    if (value.length <= 10) {
      setPhone(value);
      setErrors(prev => ({ ...prev, phone: '' }));
    }
  };

  // Handle OTP input
  const handleOTPChange = (index, value) => {
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }

    setErrors(prev => ({ ...prev, otp: '' }));
  };

  // Handle OTP backspace
  const handleOTPKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  // Handle send OTP
  const handleSendOTP = async () => {
    setErrors({});

    // Prevent duplicate calls
    if (isLoading) {
      console.log('Already sending OTP, ignoring duplicate call');
      return;
    }

    // Validate phone
    if (!validatePhone(phone)) {
      setErrors({ phone: 'Please enter a valid 10-digit phone number' });
      return;
    }

    console.log('Calling sendOTP with phone:', phone);
    const result = await sendOTP(phone);
    console.log('SendOTP result:', result);

    // The useEffect will handle step transition based on otpSent
    if (!result.success) {
      setErrors({ phone: result.error });
    } else {
      // Clear any previous errors on success
      clearError();
    }
  };

  // Handle verify OTP
  const handleVerifyOTP = async () => {
    setErrors({});

    const otpString = otp.join('');

    // Use originalPhone from AuthContext instead of local phone state
    const phoneToUse = originalPhone || phone;

    // Debug logging
    console.log('handleVerifyOTP called with local phone:', phone, 'originalPhone:', originalPhone, 'using:', phoneToUse, 'otp:', otpString.substring(0, 2) + '****');

    // Validate OTP
    if (!validateOTP(otpString)) {
      setErrors({ otp: 'Please enter a valid 6-digit OTP' });
      return;
    }

    const result = await verifyOTP(phoneToUse, otpString, userDetails);

    if (result.success) {
      if (result.data.is_new_user && (!userDetails.first_name || !userDetails.last_name)) {
        setStep('profile');
      } else {
        onSuccess?.(result.data);
      }
    }
  };

  // Handle profile completion
  const handleCompleteProfile = async () => {
    setErrors({});

    // Validate required fields
    const newErrors = {};
    if (!userDetails.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }
    if (!userDetails.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }
    if (userDetails.email && !/\S+@\S+\.\S+/.test(userDetails.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const otpString = otp.join('');
    const phoneToUse = originalPhone || phone;
    const result = await verifyOTP(phoneToUse, otpString, userDetails);

    if (result.success) {
      onSuccess?.(result.data);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    const phoneToUse = originalPhone || phone;
    const result = await resendOTP(phoneToUse);
    if (result.success) {
      setResendTimer(60);
      setOtp(['', '', '', '', '', '']);
      otpRefs.current[0]?.focus();
    }
  };

  return (
    <div className="w-full max-w-md mx-auto" data-step={step}>
      <AnimatePresence mode="wait">
        {/* Phone Number Step */}
        {step === 'phone' && (
          <motion.div
            key="phone"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="space-y-8"
          >
            <div className="text-center space-y-3">
              <motion.h2 
                className="text-3xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-300 bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                Welcome to Wolffoxx
              </motion.h2>
              <motion.p 
                className="text-gray-400 text-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                Enter your phone number to continue
              </motion.p>
            </div>

            <motion.div 
              className="space-y-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-300 mb-3">
                  Phone Number
                </label>
                <div className="relative group">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <Phone size={20} className="text-gray-500 group-focus-within:text-orange-400 transition-colors duration-300" />
                    <span className="ml-3 text-gray-400 font-medium">+91</span>
                  </div>
                  <input
                    ref={phoneInputRef}
                    type="tel"
                    value={formatPhoneDisplay(phone)}
                    onChange={handlePhoneChange}
                    placeholder="98765 43210"
                    className="w-full pl-20 pr-4 py-4 bg-gradient-to-r from-gray-900/80 to-gray-800/60 border border-gray-700/50 rounded-xl text-white placeholder-gray-500 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-300 text-lg font-medium backdrop-blur-sm"
                    style={{ minHeight: '56px' }}
                  />
                  {/* Input glow effect */}
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-500/0 to-orange-400/0 group-focus-within:from-orange-500/5 group-focus-within:to-orange-400/5 transition-all duration-300 pointer-events-none" />
                </div>
                {errors.phone && (
                  <motion.p 
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="mt-2 text-sm text-red-400 flex items-center gap-2"
                  >
                    <AlertCircle size={16} />
                    {errors.phone}
                  </motion.p>
                )}
              </div>

              <motion.button
                onClick={handleSendOTP}
                disabled={isLoading || !phone}
                className="relative w-full group overflow-hidden"
                style={{ minHeight: '56px' }}
                whileHover={{ scale: phone && !isLoading ? 1.02 : 1 }}
                whileTap={{ scale: phone && !isLoading ? 0.98 : 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                {/* Button background */}
                <div className={`absolute inset-0 rounded-xl transition-all duration-300 ${
                  phone && !isLoading 
                    ? 'bg-gradient-to-r from-orange-500 to-orange-600 group-hover:from-orange-400 group-hover:to-orange-500' 
                    : 'bg-gradient-to-r from-gray-700 to-gray-800'
                }`} />
                
                {/* Button glow */}
                {phone && !isLoading && (
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-400 to-orange-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500" />
                )}
                
                {/* Button content */}
                <div className="relative flex items-center justify-center gap-3 py-4 px-6 text-white font-semibold text-lg">
                  {isLoading ? (
                    <Loader2 size={20} className="animate-spin" />
                  ) : (
                    <>
                      Send OTP
                      <ArrowRight size={20} className="group-hover:translate-x-1 transition-transform duration-300" />
                    </>
                  )}
                </div>
              </motion.button>
            </motion.div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-red-500/10 border border-red-500/30 rounded-xl text-red-400 text-sm flex items-center gap-3 backdrop-blur-sm"
              >
                <AlertCircle size={18} />
                <span className="font-medium">{error}</span>
              </motion.div>
            )}
          </motion.div>
        )}

        {/* OTP Verification Step */}
        {step === 'otp' && (
          <motion.div
            key="otp"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="space-y-8"
          >
            <div className="text-center space-y-3">
              <motion.h2 
                className="text-3xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-300 bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                Verify OTP
              </motion.h2>
              <motion.p 
                className="text-gray-400 text-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                Enter the 6-digit code sent to
              </motion.p>
              <motion.p 
                className="text-orange-400 font-semibold text-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                +91 {formatPhoneDisplay(phone)}
              </motion.p>
            </div>

            <motion.div 
              className="space-y-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="space-y-4">
                <label className="block text-sm font-semibold text-gray-300 text-center">
                  Enter OTP
                </label>
                <div className="flex gap-3 justify-center">
                  {otp.map((digit, index) => (
                    <motion.input
                      key={index}
                      ref={el => otpRefs.current[index] = el}
                      type="text"
                      inputMode="numeric"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleOTPChange(index, e.target.value)}
                      onKeyDown={(e) => handleOTPKeyDown(index, e)}
                      className="w-14 h-14 text-center text-xl font-bold bg-gradient-to-br from-gray-900/80 to-gray-800/60 border border-gray-700/50 rounded-xl text-white focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-300 backdrop-blur-sm"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                      whileFocus={{ scale: 1.05 }}
                    />
                  ))}
                </div>
                {errors.otp && (
                  <motion.p 
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="mt-3 text-sm text-red-400 flex items-center justify-center gap-2"
                  >
                    <AlertCircle size={16} />
                    {errors.otp}
                  </motion.p>
                )}
              </div>

              <motion.button
                onClick={handleVerifyOTP}
                disabled={isLoading || otp.some(digit => !digit)}
                className="relative w-full group overflow-hidden"
                style={{ minHeight: '56px' }}
                whileHover={{ scale: otp.every(digit => digit) && !isLoading ? 1.02 : 1 }}
                whileTap={{ scale: otp.every(digit => digit) && !isLoading ? 0.98 : 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2 }}
              >
                {/* Button background */}
                <div className={`absolute inset-0 rounded-xl transition-all duration-300 ${
                  otp.every(digit => digit) && !isLoading 
                    ? 'bg-gradient-to-r from-orange-500 to-orange-600 group-hover:from-orange-400 group-hover:to-orange-500' 
                    : 'bg-gradient-to-r from-gray-700 to-gray-800'
                }`} />
                
                {/* Button glow */}
                {otp.every(digit => digit) && !isLoading && (
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-400 to-orange-500 opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500" />
                )}
                
                {/* Button content */}
                <div className="relative flex items-center justify-center gap-3 py-4 px-6 text-white font-semibold text-lg">
                  {isLoading ? (
                    <Loader2 size={20} className="animate-spin" />
                  ) : (
                    <>
                      Verify & Continue
                      <CheckCircle size={20} className="group-hover:scale-110 transition-transform duration-300" />
                    </>
                  )}
                </div>
              </motion.button>

              <motion.div 
                className="text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.4 }}
              >
                {resendTimer > 0 ? (
                  <p className="text-gray-400 text-sm font-medium">
                    Resend OTP in <span className="text-orange-400 font-semibold">{resendTimer}s</span>
                  </p>
                ) : (
                  <motion.button
                    onClick={handleResendOTP}
                    disabled={isLoading}
                    className="text-orange-400 hover:text-orange-300 text-sm font-semibold flex items-center gap-2 mx-auto transition-all duration-300 hover:scale-105 group"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <RotateCcw size={16} className="group-hover:rotate-180 transition-transform duration-500" />
                    Resend OTP
                  </motion.button>
                )}
              </motion.div>
            </motion.div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-red-500/10 border border-red-500/30 rounded-xl text-red-400 text-sm flex items-center gap-3 backdrop-blur-sm"
              >
                <AlertCircle size={18} />
                <span className="font-medium">{error}</span>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OTPLogin;