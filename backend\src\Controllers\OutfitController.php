<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Outfit;
use Wolffoxx\Models\Product;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Validator;
use Wolffoxx\Config\Database;

/**
 * Outfit Controller
 * 
 * Handles outfit management operations including
 * creating, retrieving, updating, and deleting outfits.
 */
class OutfitController extends BaseController
{
    private Outfit $outfitModel;
    private Product $productModel;

    public function __construct()
    {
        $this->outfitModel = new Outfit();
        $this->productModel = new Product();
    }

    /**
     * Get user's outfits
     * GET /api/v1/outfits
     */
    public function index(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                $userId = 3; // Default test user for development
            }

            // Get outfits with their items
            $outfits = $this->outfitModel->getUserOutfits($userId);

            Response::success([
                'outfits' => $outfits,
                'pagination' => [
                    'total' => count($outfits),
                    'limit' => 20,
                    'offset' => 0,
                    'has_more' => false
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Get outfits failed: ' . $e->getMessage());
            Response::error('Failed to retrieve outfits');
        }
    }

    /**
     * Create new outfit
     * POST /api/v1/outfits
     */
    public function create(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                $userId = 3; // Default test user for development
            }

            $input = $this->getJsonInput();

            // Simple validation
            if (empty($input['name'])) {
                Response::error('Name is required');
                return;
            }

            // Create outfit using Database class directly
            $uuid = 'outfit-' . time() . '-' . rand(1000, 9999);

            $sql = "INSERT INTO outfits (uuid, user_id, name, description, occasion, season, style, color_scheme, is_public, tags, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

            $stmt = Database::execute($sql, [
                $uuid,
                $userId,
                $input['name'],
                $input['description'] ?? null,
                $input['occasion'] ?? null,
                $input['season'] ?? 'all',
                $input['style'] ?? null,
                $input['color_scheme'] ?? null,
                $input['is_public'] ?? false,
                $input['tags'] ?? null
            ]);

            $outfitId = Database::getConnection()->lastInsertId();

            // If lastInsertId() returns 0, get the outfit ID by UUID
            if (!$outfitId) {
                $getIdSql = "SELECT id FROM outfits WHERE uuid = ?";
                $getIdStmt = Database::execute($getIdSql, [$uuid]);
                $idResult = $getIdStmt->fetch();
                $outfitId = $idResult['id'] ?? null;
            }

            // Add items if provided
            if (!empty($input['items']) && $outfitId) {
                foreach ($input['items'] as $item) {
                    if (!empty($item['product_id'])) {
                        // Get product details
                        $productSql = "SELECT * FROM products WHERE id = ?";
                        $productStmt = Database::execute($productSql, [$item['product_id']]);
                        $product = $productStmt->fetch();

                        if ($product) {
                            // Get color hex value if color is provided
                            $colorHex = null;
                            if (!empty($item['selected_color'])) {
                                $colorHex = $item['selected_color_hex'] ?? $this->getColorHex($item['selected_color']);
                            }

                            // Insert outfit item
                            $itemSql = "INSERT INTO outfit_items (
                                outfit_id, product_id, selected_color, selected_size, selected_color_hex,
                                category_type, sort_order, is_primary, price_at_addition,
                                sale_price_at_addition, created_at, updated_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

                            Database::execute($itemSql, [
                                $outfitId,
                                $item['product_id'],
                                $item['selected_color'] ?? null,
                                $item['selected_size'] ?? null,
                                $colorHex,
                                'top', // Default category type
                                0, // Default sort order
                                $item['is_primary'] ?? false,
                                $product['price'],
                                $product['sale_price']
                            ]);
                        }
                    }
                }
            }

            // Get the created outfit
            $getOutfitSql = "SELECT * FROM outfits WHERE id = ?";
            $outfitStmt = Database::execute($getOutfitSql, [$outfitId]);
            $outfit = $outfitStmt->fetch();

            Response::success([
                'outfit' => $outfit,
                'message' => 'Outfit created successfully'
            ], 201);

        } catch (\Exception $e) {
            error_log('Create outfit failed: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            Response::error('Failed to create outfit: ' . $e->getMessage());
        }
    }

    /**
     * Get single outfit with items
     * GET /api/v1/outfits/{id}
     */
    public function show(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            if (!$outfit) {
                Response::notFound('Outfit not found');
                return;
            }

            Response::success($outfit);

        } catch (\Exception $e) {
            Response::error('Failed to retrieve outfit');
        }
    }

    /**
     * Update outfit
     * PUT /api/v1/outfits/{id}
     */
    public function update(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'name' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:1000',
                'occasion' => 'nullable|string|max:100',
                'season' => 'nullable|string|max:20',
                'style' => 'nullable|string|max:100',
                'color_scheme' => 'nullable|string|max:100',
                'is_public' => 'nullable|boolean',
                'tags' => 'nullable|string|max:500'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $outfit = $this->outfitModel->update($outfitId, $data);

            if (!$outfit) {
                Response::error('Failed to update outfit');
                return;
            }

            Response::success([
                'outfit' => $outfit,
                'message' => 'Outfit updated successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to update outfit');
        }
    }

    /**
     * Delete outfit
     * DELETE /api/v1/outfits/{id}
     */
    public function delete(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $deleted = $this->outfitModel->deleteOutfit($outfitId);

            if (!$deleted) {
                Response::error('Failed to delete outfit');
                return;
            }

            Response::success([
                'message' => 'Outfit deleted successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to delete outfit');
        }
    }

    /**
     * Add item to outfit
     * POST /api/v1/outfits/{id}/items
     */
    public function addItem(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'product_id' => 'required|integer',
                'selected_color' => 'nullable|string|max:100',
                'selected_size' => 'nullable|string|max:20',
                'selected_color_hex' => 'nullable|string|max:7',
                'category_type' => 'nullable|string|max:50',
                'is_primary' => 'nullable|boolean'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $result = $this->outfitModel->addItemToOutfit($outfitId, $data);

            if (!$result) {
                Response::error('Failed to add item to outfit');
                return;
            }

            // Get updated outfit
            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            Response::success([
                'outfit' => $outfit,
                'message' => 'Item added to outfit successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to add item to outfit');
        }
    }

    /**
     * Remove item from outfit
     * DELETE /api/v1/outfits/{id}/items/{itemId}
     */
    public function removeItem(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            $itemId = (int)$params['itemId'];

            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $result = $this->outfitModel->removeItemFromOutfit($outfitId, $itemId);

            if (!$result) {
                Response::error('Failed to remove item from outfit');
                return;
            }

            // Get updated outfit
            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            Response::success([
                'outfit' => $outfit,
                'message' => 'Item removed from outfit successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to remove item from outfit');
        }
    }

    /**
     * Get outfit collections (placeholder for future implementation)
     * GET /api/v1/outfits/collections
     */
    public function getCollections(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // For now, return empty collections
            // This can be expanded later when collections feature is needed
            Response::success([
                'collections' => [],
                'message' => 'Collections feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to retrieve collections');
        }
    }

    /**
     * Create outfit collection (placeholder for future implementation)
     * POST /api/v1/outfits/collections
     */
    public function createCollection(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Collections feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to create collection');
        }
    }

    /**
     * Toggle like on outfit (placeholder for future implementation)
     * POST /api/v1/outfits/{id}/like
     */
    public function toggleLike(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Like feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to toggle like');
        }
    }

    /**
     * Share outfit (placeholder for future implementation)
     * POST /api/v1/outfits/{id}/share
     */
    public function shareOutfit(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Share feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to share outfit');
        }
    }

    /**
     * Get color hex value from color name
     */
    private function getColorHex(string $colorName): ?string
    {
        // Common color mappings
        $colorMap = [
            // Basic colors
            'Black' => '#000000',
            'White' => '#FFFFFF',
            'Red' => '#DC2626',
            'Blue' => '#2563EB',
            'Green' => '#16A34A',
            'Yellow' => '#EAB308',
            'Orange' => '#EA580C',
            'Purple' => '#9333EA',
            'Pink' => '#EC4899',
            'Brown' => '#A16207',
            'Gray' => '#6B7280',
            'Grey' => '#6B7280',

            // Extended colors
            'Navy' => '#1E3A8A',
            'Navy Blue' => '#1E3A8A',
            'Light Blue' => '#3B82F6',
            'Dark Blue' => '#1E40AF',
            'Sky Blue' => '#0EA5E9',
            'Royal Blue' => '#2563EB',

            'Dark Green' => '#15803D',
            'Light Green' => '#22C55E',
            'Forest Green' => '#166534',
            'Olive' => '#84CC16',
            'Sage Green' => '#9CA3AF',

            'Maroon' => '#991B1B',
            'Burgundy' => '#7C2D12',
            'Wine' => '#7C2D12',

            'Beige' => '#F5F5DC',
            'Cream' => '#FFFDD0',
            'Ivory' => '#FFFFF0',
            'Off White' => '#FAF9F6',

            'Light Gray' => '#D1D5DB',
            'Dark Gray' => '#374151',
            'Charcoal' => '#1F2937',
            'Silver' => '#9CA3AF',

            'Light Wash' => '#6B9BD1',
            'Dark Wash' => '#1E3A8A',
            'Stone Wash' => '#9CA3AF',

            // Fashion colors
            'Khaki' => '#F0E68C',
            'Tan' => '#D2B48C',
            'Camel' => '#C19A6B',
            'Mustard' => '#FFDB58',
            'Rust' => '#B7410E',
            'Coral' => '#FF7F50',
            'Teal' => '#14B8A6',
            'Mint' => '#6EE7B7',
            'Lavender' => '#E879F9',
            'Rose' => '#F43F5E',
            'Peach' => '#FDBA74'
        ];

        // Try exact match first
        if (isset($colorMap[$colorName])) {
            return $colorMap[$colorName];
        }

        // Try case-insensitive match
        foreach ($colorMap as $name => $hex) {
            if (strcasecmp($name, $colorName) === 0) {
                return $hex;
            }
        }

        // Try to find from product_colors table
        try {
            $sql = "SELECT hex_value FROM product_colors WHERE name = ? LIMIT 1";
            $stmt = Database::execute($sql, [$colorName]);
            $result = $stmt->fetch();
            if ($result) {
                return $result['hex_value'];
            }
        } catch (\Exception $e) {
            // Fallback to default
        }

        // Default fallback color
        return '#6B7280'; // Gray
    }
}
