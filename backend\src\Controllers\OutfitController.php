<?php

namespace Wolffoxx\Controllers;

use Wolffoxx\Models\Outfit;
use Wolffoxx\Models\Product;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Validator;

/**
 * Outfit Controller
 * 
 * Handles outfit management operations including
 * creating, retrieving, updating, and deleting outfits.
 */
class OutfitController extends BaseController
{
    private Outfit $outfitModel;
    private Product $productModel;

    public function __construct()
    {
        $this->outfitModel = new Outfit();
        $this->productModel = new Product();
    }

    /**
     * Get user's outfits
     * GET /api/v1/outfits
     */
    public function index(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Simple query to get outfits for user
            $sql = "SELECT * FROM outfits WHERE user_id = ? ORDER BY created_at DESC";
            $stmt = \Wolffoxx\Config\Database::execute($sql, [$userId]);
            $outfits = $stmt->fetchAll();

            Response::success([
                'outfits' => $outfits,
                'pagination' => [
                    'total' => count($outfits),
                    'limit' => 20,
                    'offset' => 0,
                    'has_more' => false
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Get outfits failed: ' . $e->getMessage());
            Response::error('Failed to retrieve outfits');
        }
    }

    /**
     * Create new outfit
     * POST /api/v1/outfits
     */
    public function create(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                $userId = 3; // Default test user for development
            }

            $input = $this->getJsonInput();

            // Debug logging - write to a specific file
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Outfit creation request - User ID: $userId\n", FILE_APPEND);
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Input: " . json_encode($input) . "\n", FILE_APPEND);

            // Validate input
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - About to create Validator\n", FILE_APPEND);

            $validator = new Validator($input, [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'occasion' => 'nullable|string|max:100',
                'season' => 'nullable|string|max:20',
                'style' => 'nullable|string|max:100',
                'color_scheme' => 'nullable|string|max:100',
                'is_public' => 'nullable|boolean',
                'tags' => 'nullable|string|max:500',
                'items' => 'nullable|array'
            ]);

            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Validator created, about to validate\n", FILE_APPEND);

            if (!$validator->validate()) {
                file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                    date('Y-m-d H:i:s') . " - Validation failed: " . json_encode($validator->getErrors()) . "\n", FILE_APPEND);
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Validated data: " . json_encode($data) . "\n", FILE_APPEND);

            // Use the Outfit model to create outfit with items
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Calling createOutfit with user $userId\n", FILE_APPEND);

            $outfit = $this->outfitModel->createOutfit($userId, $data);

            if (!$outfit) {
                file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                    date('Y-m-d H:i:s') . " - createOutfit returned null\n", FILE_APPEND);
                Response::error('Failed to create outfit');
                return;
            }

            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Outfit created with ID: " . $outfit['id'] . "\n", FILE_APPEND);

            // Get the complete outfit with items
            $completeOutfit = $this->outfitModel->getOutfitWithItems($outfit['id']);
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Complete outfit retrieved\n", FILE_APPEND);

            Response::success([
                'outfit' => $completeOutfit,
                'message' => 'Outfit created successfully'
            ], 201);

        } catch (\Exception $e) {
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Exception: " . $e->getMessage() . "\n", FILE_APPEND);
            file_put_contents(__DIR__ . '/../../storage/logs/outfit_debug.log',
                date('Y-m-d H:i:s') . " - Stack trace: " . $e->getTraceAsString() . "\n", FILE_APPEND);
            Response::error('Failed to create outfit: ' . $e->getMessage());
        }
    }

    /**
     * Get single outfit with items
     * GET /api/v1/outfits/{id}
     */
    public function show(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            if (!$outfit) {
                Response::notFound('Outfit not found');
                return;
            }

            Response::success($outfit);

        } catch (\Exception $e) {
            Response::error('Failed to retrieve outfit');
        }
    }

    /**
     * Update outfit
     * PUT /api/v1/outfits/{id}
     */
    public function update(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'name' => 'nullable|string|max:255',
                'description' => 'nullable|string|max:1000',
                'occasion' => 'nullable|string|max:100',
                'season' => 'nullable|string|max:20',
                'style' => 'nullable|string|max:100',
                'color_scheme' => 'nullable|string|max:100',
                'is_public' => 'nullable|boolean',
                'tags' => 'nullable|string|max:500'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $outfit = $this->outfitModel->update($outfitId, $data);

            if (!$outfit) {
                Response::error('Failed to update outfit');
                return;
            }

            Response::success([
                'outfit' => $outfit,
                'message' => 'Outfit updated successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to update outfit');
        }
    }

    /**
     * Delete outfit
     * DELETE /api/v1/outfits/{id}
     */
    public function delete(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $deleted = $this->outfitModel->deleteOutfit($outfitId);

            if (!$deleted) {
                Response::error('Failed to delete outfit');
                return;
            }

            Response::success([
                'message' => 'Outfit deleted successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to delete outfit');
        }
    }

    /**
     * Add item to outfit
     * POST /api/v1/outfits/{id}/items
     */
    public function addItem(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            
            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'product_id' => 'required|integer',
                'selected_color' => 'nullable|string|max:100',
                'selected_size' => 'nullable|string|max:20',
                'selected_color_hex' => 'nullable|string|max:7',
                'category_type' => 'nullable|string|max:50',
                'is_primary' => 'nullable|boolean'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $result = $this->outfitModel->addItemToOutfit($outfitId, $data);

            if (!$result) {
                Response::error('Failed to add item to outfit');
                return;
            }

            // Get updated outfit
            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            Response::success([
                'outfit' => $outfit,
                'message' => 'Item added to outfit successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to add item to outfit');
        }
    }

    /**
     * Remove item from outfit
     * DELETE /api/v1/outfits/{id}/items/{itemId}
     */
    public function removeItem(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $outfitId = (int)$params['id'];
            $itemId = (int)$params['itemId'];

            // Check if user owns the outfit
            if (!$this->outfitModel->isUserOutfit($outfitId, $userId)) {
                Response::forbidden('Access denied');
                return;
            }

            $result = $this->outfitModel->removeItemFromOutfit($outfitId, $itemId);

            if (!$result) {
                Response::error('Failed to remove item from outfit');
                return;
            }

            // Get updated outfit
            $outfit = $this->outfitModel->getOutfitWithItems($outfitId);

            Response::success([
                'outfit' => $outfit,
                'message' => 'Item removed from outfit successfully'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to remove item from outfit');
        }
    }

    /**
     * Get outfit collections (placeholder for future implementation)
     * GET /api/v1/outfits/collections
     */
    public function getCollections(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // For now, return empty collections
            // This can be expanded later when collections feature is needed
            Response::success([
                'collections' => [],
                'message' => 'Collections feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to retrieve collections');
        }
    }

    /**
     * Create outfit collection (placeholder for future implementation)
     * POST /api/v1/outfits/collections
     */
    public function createCollection(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Collections feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to create collection');
        }
    }

    /**
     * Toggle like on outfit (placeholder for future implementation)
     * POST /api/v1/outfits/{id}/like
     */
    public function toggleLike(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Like feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to toggle like');
        }
    }

    /**
     * Share outfit (placeholder for future implementation)
     * POST /api/v1/outfits/{id}/share
     */
    public function shareOutfit(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            Response::success([
                'message' => 'Share feature coming soon'
            ]);

        } catch (\Exception $e) {
            Response::error('Failed to share outfit');
        }
    }
}
