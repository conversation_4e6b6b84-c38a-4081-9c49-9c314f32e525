import React, { useState, useEffect, useRef, Suspense, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion, useScroll, useTransform, useSpring, AnimatePresence, useMotionValue, useVelocity } from 'framer-motion';
import { ArrowRight, ChevronDown, Star, Play, Volume2, VolumeX, ArrowUpRight, Eye, Heart, ShoppingBag, Zap, Shield, Truck, RefreshCw, Award, Plus, Minus, Crown, Flame, Users, ChevronLeft, ChevronRight, Search, MapPin } from 'lucide-react';
import { dataService } from '../services/dataService';
import WishlistButton from '../components/WishlistButton';
import FeaturedCollection from '../components/FeaturedCollection';
import EnhancedStatsSection from './EnhancedStatsSection';
import EnhancedHeroSection from './HeroSection';

// Performance optimized components
const MagneticButton = React.lazy(() => import('../components/MagneticButton'));
// const MarqueeText = React.lazy(() => import('../components/MarqueeText')); // Commented out - not used
const ColorPreviewSlider = React.lazy(() => import('../components/ColorPreviewSlider'));

// Enhanced Loading Components
const PremiumLoader = ({ height = 'py-12', text = 'Loading...', variant = 'spin' }) => (
  <div className={`${height} flex items-center justify-center`}>
    {variant === 'spin' ? (
      <div className="relative">
        <div className="w-12 h-12 border-4 border-blue-600/20 border-t-blue-600 rounded-full animate-spin" />
        <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-cyan-600 rounded-full animate-spin animate-reverse" style={{ animationDelay: '0.5s' }} />
      </div>
    ) : (
      <div className="flex space-x-2">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="w-3 h-3 bg-blue-600 rounded-full animate-bounce"
            style={{ animationDelay: `${i * 0.2}s` }}
          />
        ))}
      </div>
    )}
    <span className="ml-4 text-gray-400 text-sm font-medium">{text}</span>
  </div>
);

// Floating Elements
const FloatingElements = () => {
  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 bg-blue-500/20 rounded-full"
          animate={{
            y: [0, -120, 0],
            x: [0, Math.random() * 120 - 60, 0],
            opacity: [0, 0.8, 0],
          }}
          transition={{
            duration: 4 + Math.random() * 3,
            repeat: Infinity,
            delay: i * 0.7,
          }}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${100 + Math.random() * 100}%`,
          }}
        />
      ))}
    </div>
  );
};

export default function HomePage() {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isVideoMuted, setIsVideoMuted] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Scroll indicators state
  const [bestSellerScrollIndex, setBestSellerScrollIndex] = useState(0);

  // Navigation hook for search redirection
  const navigate = useNavigate();

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  const containerRef = useRef(null);
  const heroVideoRef = useRef(null);
  // const lookbookRef = useRef(null); // COMMENTED OUT - Lookbook section disabled
  const featuredRef = useRef(null);
  const bestSellerScrollRef = useRef(null);

  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, 100]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0]);

  // Enhanced parallax effects for different sections
  const parallaxY1 = useTransform(scrollY, [0, 1000], [0, -100]);
  const parallaxY2 = useTransform(scrollY, [0, 1000], [0, 50]);
  const parallaxY3 = useTransform(scrollY, [0, 1500], [0, -75]);
  const scaleEffect = useTransform(scrollY, [0, 500], [1, 1.1]);
  const rotateEffect = useTransform(scrollY, [0, 1000], [0, 5]);

  // Mouse tracking for magnetic effects
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const mouseXSpring = useSpring(mouseX);
  const mouseYSpring = useSpring(mouseY);

  // Scroll handlers for indicators
  const handleBestSellerScroll = () => {
    if (bestSellerScrollRef.current) {
      const container = bestSellerScrollRef.current;
      const scrollLeft = container.scrollLeft;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16; // gap-4
      const totalItemWidth = itemWidth + gap;
      const index = Math.round(scrollLeft / totalItemWidth);
      setBestSellerScrollIndex(Math.min(index, featuredProducts.length - 1));
    }
  };





  // Scroll to item functions
  const scrollToBestSeller = (index) => {
    if (bestSellerScrollRef.current) {
      const container = bestSellerScrollRef.current;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16;
      const totalItemWidth = itemWidth + gap;
      const scrollLeft = index * totalItemWidth;
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    }
  };





  // Intersection Observer with enhanced animations
  // const [isLookbookVisible, setIsLookbookVisible] = useState(false); // COMMENTED OUT - Lookbook section disabled
  const [isFeaturedVisible, setIsFeaturedVisible] = useState(false);

  useEffect(() => {
    const observers = [
      // {
      //   ref: lookbookRef,
      //   setter: setIsLookbookVisible,
      // }, // COMMENTED OUT - Lookbook section disabled
      {
        ref: featuredRef,
        setter: setIsFeaturedVisible,
      },
    ];

    const observerInstances = observers.map(({ ref, setter }) => {
      const observer = new IntersectionObserver(
        ([entry]) => setter(entry.isIntersecting),
        { threshold: 0.1, rootMargin: '100px' }
      );
      if (ref.current) observer.observe(ref.current);
      return { observer, ref };
    });

    return () => {
      observerInstances.forEach(({ observer, ref }) => {
        if (ref.current) observer.unobserve(ref.current);
      });
    };
  }, []);

  // Enhanced mouse tracking
  const handleMouseMove = useCallback((e) => {
    const { clientX, clientY } = e;
    setMousePosition({ x: clientX, y: clientY });
    mouseX.set(clientX);
    mouseY.set(clientY);
  }, [mouseX, mouseY]);

  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [handleMouseMove]);

  // Load featured products from API
  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        setLoading(true);
        const products = await dataService.getBestsellerProducts(8);
        setFeaturedProducts(products);
      } catch (error) {
        console.error('Failed to load featured products:', error);
        // Fallback to empty array - component will handle gracefully
        setFeaturedProducts([]);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);

  // Scroll to top when component mounts (when navigating back to homepage)
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Add scroll event listeners for indicators
  useEffect(() => {
    const bestSellerContainer = bestSellerScrollRef.current;

    if (bestSellerContainer) {
      bestSellerContainer.addEventListener('scroll', handleBestSellerScroll);
    }

    return () => {
      if (bestSellerContainer) {
        bestSellerContainer.removeEventListener('scroll', handleBestSellerScroll);
      }
    };
  }, []);

  // const lookbookImages = [ // COMMENTED OUT - Lookbook section disabled
  //   "https://images.unsplash.com/photo-1551833726-b6e7210484c2?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1499939667766-4afceb292d05?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1536243298747-ea8874136d64?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1507680434567-5739c80be1ac?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1550123297-69e7c3b3e9d1?q=80&w=700&auto=format&fit=crop",
  //   "https://images.unsplash.com/photo-1516826957135-700dedea698c?q=80&w=700&auto=format&fit=crop"
  // ];

  const handleExploreClick = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: 'smooth',
    });
  };

  // Handler for search bar click - redirect to search page
  const handleSearchClick = () => {
    navigate('/search');
  };



  return (
    <div className="bg-black overflow-x-hidden" ref={containerRef}>
      <FloatingElements />

      {/* Enhanced Marquee - COMMENTED OUT */}
      {/* <Suspense fallback={<div className="h-8 bg-slate-800" />}>
        <motion.div
          initial={{ y: -50 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <MarqueeText
            text="🔥 FREE WORLDWIDE SHIPPING ON ALL ORDERS OVER $150 • ✨ NEW SUMMER COLLECTION DROPPING SOON • 💥 20% OFF FOR MEMBERS • 🚀 LIMITED TIME EXCLUSIVE DROPS"
            className="border-b border-slate-700 h-12 bg-gradient-to-r from-slate-900 via-blue-900/30 to-slate-900 text-white"
            speed={60}
          />
        </motion.div>
      </Suspense> */}

      {/* Revolutionary Hero Section with Video Background */}
      <section className="relative h-screen flex flex-col overflow-hidden bg-black">
        {/* Video Background */}
        <div className="absolute inset-0 z-0">
          <video
            ref={heroVideoRef}
            className="w-full h-full object-cover"
            autoPlay
            muted
            loop
            playsInline
          >
            <source src="/videos/wollfoxx_hero_video.mp4" type="video/mp4" />
            {/* Fallback image if video doesn't load */}
            <img
              src="https://images.unsplash.com/photo-1574201635302-388dd92a4c3f?q=80&w=2070&auto=format&fit=crop"
              alt="WOLFFOXX hero background"
              className="object-cover w-full h-full"
            />
          </video>
          {/* Dark overlay for better text readability */}
          <div className="absolute inset-0 bg-black/50" />
        </div>

        {/* Search Bar Section - Moved directly below navbar */}
        <motion.div
          className="relative z-10 pt-2 pb-4"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="container mx-auto px-4 max-w-4xl">
            <div
              onClick={handleSearchClick}
              className="relative flex items-center bg-transparent backdrop-blur-sm rounded-lg border border-[#DDDDDD] overflow-hidden cursor-pointer hover:border-white transition-all duration-300 group"
            >
              <Search size={18} className="ml-3 text-[#f5f5f5] group-hover:text-white transition-colors" />
              <input
                type="text"
                placeholder="Search 'trouser'"
                className="w-full py-2 px-3 bg-transparent text-[#f5f5f5] placeholder:text-[#AAAAAA] border-none outline-none cursor-pointer text-sm"
                readOnly
              />
            </div>
          </div>
        </motion.div>

        {/* Spacer to push scroll indicator to bottom */}
        <div className="flex-1"></div>

        {/* Scroll Down Indicator */}
        {/* <motion.div
          className="absolute bottom-8 left-0 right-0 flex justify-center relative z-10"
          style={{ y: y2 }}
        >
          <motion.div
            className="flex flex-col items-center text-white/70 gap-3 cursor-pointer group"
            animate={{ y: [0, 15, 0] }}
            transition={{ repeat: Infinity, duration: 2.5, ease: "easeInOut" }}
            onClick={handleExploreClick}
            whileHover={{ scale: 1.1 }}
          >
            <span className="text-sm font-medium tracking-wider">Explore Collection</span>
            <motion.div
              className="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center p-2"
              whileHover={{ borderColor: 'rgba(255, 255, 255, 0.6)' }}
            >
              <motion.div
                className="w-2 h-3 bg-white/50 rounded-full"
                animate={{ y: [0, 8, 0] }}
                transition={{ repeat: Infinity, duration: 1.5 }}
              />
            </motion.div>
          </motion.div>
        </motion.div> */}
      </section>

      {/* NEW FEATURED CATEGORIES SECTION - RIGHT AFTER HERO */}
      <Suspense fallback={<PremiumLoader height="py-12" text="Loading categories" />}>
        <FeaturedCategoriesSection />
      </Suspense>

      {/* Enhanced Streetwear Lookbook with Vertical Video - COMMENTED OUT */}
      {/* <section ref={lookbookRef} className="py-16 md:py-24 bg-black overflow-hidden relative">

        <div className="container mx-auto px-4 md:px-8 relative z-10">
          <motion.div
            className="text-center mb-12 md:mb-16"
            initial={{ opacity: 0, y: 50 }}
            animate={isLookbookVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <motion.h2
              className="text-4xl md:text-5xl lg:text-7xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-4 md:mb-6"
              whileHover={{ scale: 1.02 }}
            >
              STREETWEAR LOOKBOOK
            </motion.h2>
            <p className="text-gray-400 text-base md:text-lg max-w-3xl mx-auto leading-relaxed px-4">
              Styling inspiration for the urban explorer. Mix and match our oversized pieces
              for the ultimate street aesthetic that defines your unique identity.
            </p>
          </motion.div>

          {/* Mobile-First Grid Layout with Vertical Video */}
          {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
            {/* Featured Vertical Video - Takes full height on mobile */}
            {/* <motion.div
              className="relative group cursor-pointer lg:row-span-2 h-[500px] md:h-[600px] lg:h-[700px]"
              initial={{ opacity: 0, y: 50, rotateX: 10 }}
              animate={isLookbookVisible ? {
                opacity: 1,
                y: 0,
                rotateX: 0
              } : {}}
              transition={{
                duration: 0.8,
                delay: 0.1,
                type: "spring",
                stiffness: 100
              }}
              whileHover={{
                y: -5,
                scale: 1.02
              }}
            >
              <div className="relative w-full h-full rounded-2xl overflow-hidden bg-[#0a0a0a] shadow-2xl">
                <video
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                  autoPlay
                  muted
                  loop
                  playsInline
                  poster="https://images.unsplash.com/photo-1551833726-b6e7210484c2?q=80&w=400&auto=format&fit=crop"
                >
                  <source src="/videos/streetwear-lookbook-vertical.mp4" type="video/mp4" />
                  {/* Fallback image if video doesn't load */}
                  {/* <img
                    src="https://images.unsplash.com/photo-1551833726-b6e7210484c2?q=80&w=400&auto=format&fit=crop"
                    alt="Streetwear Lookbook"
                    className="w-full h-full object-cover"
                  />
                </video>

                {/* Video Overlay */}
                {/* <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Play Icon Indicator */}
                {/* <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <div className="w-0 h-0 border-l-[6px] border-l-white border-y-[4px] border-y-transparent ml-1"></div>
                  </div>
                </div>

                {/* Content Overlay */}
                {/* <div className="absolute bottom-0 left-0 right-0 p-4 md:p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500">
                  <h3 className="text-white font-bold text-lg md:text-xl mb-2">
                    STREET STYLE ESSENTIALS
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Watch how our oversized pieces create the perfect street aesthetic
                  </p>
                </div>
              </div>
            </motion.div> */}

            {/* Image Grid Items */}
            {/* {lookbookImages.slice(0, 5).map((image, index) => (
              <motion.div
                key={index}
                className={`relative group cursor-pointer h-[240px] md:h-[280px] ${
                  index === 2 ? 'md:col-span-2' : ''
                }`}
                initial={{ opacity: 0, y: 50, rotateX: 10 }}
                animate={isLookbookVisible ? {
                  opacity: 1,
                  y: 0,
                  rotateX: 0
                } : {}}
                transition={{
                  duration: 0.8,
                  delay: (index + 1) * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -5,
                  scale: 1.02
                }}
              >
                <div className="relative w-full h-full rounded-2xl overflow-hidden bg-[#0a0a0a] shadow-2xl">
                  <img
                    src={image}
                    alt={`Lookbook ${index + 1}`}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    loading="lazy"
                  />

                  {/* Gradient Overlay */}
                  {/* <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Floating Elements */}
                  {/* <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className="absolute top-3 left-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                    </div>
                    <div className="absolute top-3 right-3">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.5s'}} />
                    </div>
                  </div>

                  {/* Content Overlay */}
                  {/* <div className="absolute bottom-0 left-0 right-0 p-3 md:p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-500">
                    <h3 className="text-white font-bold text-sm md:text-base mb-1">
                      {['OVERSIZED VIBES', 'STREET READY', 'URBAN COMFORT', 'LAYERED LOOKS', 'BOLD STATEMENTS'][index]}
                    </h3>
                    <p className="text-gray-300 text-xs md:text-sm">
                      {['Perfect oversized fits', 'Street-ready combinations', 'Comfort meets style', 'Master the art of layering', 'Make a statement'][index]}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))} */}
          {/* </div>

          <motion.div
            className="text-center mt-12 md:mt-16"
            initial={{ opacity: 0 }}
            animate={isLookbookVisible ? { opacity: 1 } : {}}
            transition={{ delay: 1 }}
          >
            <Link
              to="/lookbook"
              className="group inline-flex items-center gap-3 text-white text-base md:text-lg border-b-2 border-blue-500 pb-2 font-medium hover:text-blue-400 transition-all duration-300"
            >
              EXPLORE FULL LOOKBOOK
              <ArrowRight size={20} className="group-hover:translate-x-2 transition-transform" />
            </Link>
          </motion.div>
        </div>
      </section> */}

      <Suspense fallback={<PremiumLoader height="py-24" text="Loading statistics" />}>
        <EnhancedStatsSection/>
      </Suspense>

      {/* Enhanced Why Choose Us */}
      <Suspense fallback={<PremiumLoader height="py-12" />}>
        <WhyChooseUsOptimized />
      </Suspense>

      {/* Enhanced Testimonials */}
      <Suspense fallback={<PremiumLoader height="py-16" text="Loading testimonials" />}>
        <TestimonialsSectionOptimized />
      </Suspense>

      {/* Enhanced Subscription */}
      <Suspense fallback={<PremiumLoader height="py-12" />}>
        <SubscriptionSectionOptimized />
      </Suspense>

      {/* Enhanced Instagram Feed */}
      <Suspense fallback={<PremiumLoader height="py-12" text="Loading Instagram feed" />}>
        <InstagramFeedOptimized />
      </Suspense>

      {/* NEW FAQ Section */}
      <Suspense fallback={<PremiumLoader height="py-16" text="Loading FAQ" />}>
        <FAQSection />
      </Suspense>
    </div>
  );
}

const FeaturedCategoriesSection = () => {
  const categories = [
    // Special Categories First
    {
      id: 1,
      name: "DEAL OF THE DAY",
      subtitle: "UP TO 40% OFF*",
      // description: "selected products",
      // note: "Online Exclusive",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-orange-600 to-red-600",
      textColor: "text-white",
      isSpecial: true,
      route: "/deals"
    },
    {
      id: 2,
      name: "BESTSELLERS",
      image: "https://images.unsplash.com/photo-1618354691438-25bc04584c23?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-gray-800 to-gray-900",
      textColor: "text-white",
      route: "/category/bestsellers"
    },

    // All Oversized Items Together
    {
      id: 3,
      name: "OVERSIZED TEES",
      image: "https://images.unsplash.com/photo-1576566588028-4147f3842f27?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-violet-600 to-indigo-600",
      textColor: "text-white",
      route: "/category/oversized-tees"
    },
    // {
    //   id: 4,
    //   name: "OVERSIZED T-SHIRT",
    //   image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?q=80&w=700&auto=format&fit=crop",
    //   bgColor: "from-orange-600 to-yellow-600",
    //   textColor: "text-white",
    //   route: "/category/oversized-t-shirt"
    // },
    {
      id: 5,
      name: "OVERSIZED SHIRT",
      image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-purple-600 to-pink-600",
      textColor: "text-white",
      route: "/category/oversized-shirt"
    },

    // All Shirts Together
    {
      id: 6,
      name: "SHIRTS",
      image: "https://images.unsplash.com/photo-1607345366928-199ea26cfe3e?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-blue-600 to-indigo-600",
      textColor: "text-white",
      route: "/category/shirts"
    },
    {
      id: 7,
      name: "FULL SLEEVES SHIRT",
      image: "https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-green-600 to-teal-600",
      textColor: "text-white",
      route: "/category/full-sleeves-shirt"
    },
    {
      id: 8,
      name: "SHACKET",
      image: "https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-amber-600 to-orange-600",
      textColor: "text-white",
      route: "/category/shacket"
    },

    // All T-Shirts Together
    {
      id: 9,
      name: "T-SHIRTS",
      image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-cyan-600 to-blue-600",
      textColor: "text-white",
      route: "/category/t-shirts"
    },
    {
      id: 10,
      name: "REGULAR FIT TSHIRT",
      image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-teal-600 to-cyan-600",
      textColor: "text-white",
      route: "/category/regular-fit-t-shirt"
    },

    // All Jeans Together
    {
      id: 11,
      name: "BAGGY JEANS",
      image: "https://images.unsplash.com/photo-1542272604-787c3835535d?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-cyan-600 to-blue-600",
      textColor: "text-white",
      route: "/category/baggy-jeans"
    },
    {
      id: 12,
      name: "FIT JEANS",
      image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-rose-600 to-pink-600",
      textColor: "text-white",
      route: "/category/fit-jeans"
    },

    // Other Categories
    {
      id: 13,
      name: "HOODIES",
      image: "https://images.unsplash.com/photo-1512514076443-1eef59c260b0?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-emerald-600 to-green-600",
      textColor: "text-white",
      route: "/category/hoodies"
    },
    {
      id: 14,
      name: "SWEATSHIRT",
      image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-gray-700 to-gray-800",
      textColor: "text-white",
      route: "/category/sweatshirt"
    },
    {
      id: 15,
      name: "JACKETS",
      image: "https://images.unsplash.com/photo-1551028719-00167b16eac5?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-indigo-600 to-purple-600",
      textColor: "text-white",
      route: "/category/jackets"
    },
    {
      id: 16,
      name: "CAPRI",
      image: "https://images.unsplash.com/photo-1506629905607-d9b1b2e3d8b7?q=80&w=700&auto=format&fit=crop",
      bgColor: "from-pink-600 to-rose-600",
      textColor: "text-white",
      route: "/category/capri"
    }
  ];

  return (
    <section className="py-8 bg-black min-h-screen">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-4xl font-bold text-white tracking-wider">
            FEATURED CATEGORIES
          </h2>
        </div>

        {/* Categories Grid - Enhanced Mobile Responsive Layout */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-6 max-w-7xl mx-auto">
          {categories.slice(0, 16).map((category) => (
            <Link
              key={category.id}
              to={category.route}
              className="relative group cursor-pointer overflow-hidden rounded-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl w-full block h-48 sm:h-56 md:h-64 lg:h-72"
            >
              {/* Background Image */}
              <div className="absolute inset-0">
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className={`absolute inset-0 transition-all duration-300 ${
                  category.isSpecial
                    ? 'bg-gradient-to-t from-black/80 via-black/40 to-black/20 group-hover:from-black/90'
                    : 'bg-black/30 group-hover:bg-black/50'
                }`} />
              </div>

              {/* Enhanced Content for Deal of the Day */}
              <div className="relative z-10 h-full flex flex-col justify-end p-3 sm:p-4 md:p-6 lg:p-8">
                {category.isSpecial ? (
                  <div className="space-y-2 sm:space-y-3">
                    {/* Enhanced Deal Badge */}
                    <div className="relative">
                      <div className="bg-gradient-to-r from-red-600 to-red-500 text-white text-xs sm:text-sm md:text-base font-bold px-2 sm:px-3 py-1 sm:py-2 rounded-lg w-fit shadow-lg border border-red-400/30">
                        <span className="relative z-10">DEAL OF THE DAY</span>
                        <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                      {/* Pulsing effect */}
                      <div className="absolute inset-0 bg-red-500/30 rounded-lg animate-pulse opacity-50" />
                    </div>

                    {/* Enhanced Typography */}
                    <h3 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-white leading-tight">
                      <span className="block">UP TO</span>
                      <span className="block text-yellow-400 text-shadow-lg">40% OFF*</span>
                    </h3>

                    {/* Enhanced Description */}
                    <p className="text-xs sm:text-sm md:text-base text-white/90 font-medium">
                      {category.description}
                    </p>

                    {/* Enhanced Note */}
                    <p className="text-xs sm:text-sm text-yellow-300/80 font-medium">
                      {category.note}
                    </p>

                    {/* Mobile-optimized CTA */}
                    <div className="pt-1 sm:pt-2">
                      <div className="inline-flex items-center text-white/90 text-xs sm:text-sm font-medium bg-white/10 backdrop-blur-sm rounded-full px-2 sm:px-3 py-1 sm:py-2 border border-white/20 group-hover:bg-white/20 transition-all duration-300">
                        <span>Shop Now</span>
                        <ArrowRight size={12} className="ml-1 sm:ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div>
                    <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-bold text-white leading-tight tracking-wide group-hover:text-cyan-300 transition-colors duration-300">
                      {category.name}
                    </h3>
                  </div>
                )}
              </div>

              {/* Enhanced Hover Effects */}
              {category.isSpecial && (
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                  <div className="absolute inset-0 bg-gradient-to-t from-red-900/20 via-transparent to-orange-900/20" />
                  <div className="absolute top-2 right-2 w-2 h-2 bg-yellow-400 rounded-full animate-ping" />
                </div>
              )}
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};



const CategoryTagsOptimized = () => {
  const [categoryScrollIndex, setCategoryScrollIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const categoryScrollRef = useRef(null);

  const handleCategoryScroll = () => {
    if (categoryScrollRef.current) {
      const container = categoryScrollRef.current;
      const scrollLeft = container.scrollLeft;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16; // gap-4
      const totalItemWidth = itemWidth + gap;
      const index = Math.round(scrollLeft / totalItemWidth);
      setCategoryScrollIndex(Math.min(index, 3)); // 4 categories
    }
  };

  const scrollToCategory = (index) => {
    if (categoryScrollRef.current) {
      const container = categoryScrollRef.current;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16;
      const totalItemWidth = itemWidth + gap;
      const scrollLeft = index * totalItemWidth;
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    const categoryContainer = categoryScrollRef.current;
    if (categoryContainer) {
      categoryContainer.addEventListener('scroll', handleCategoryScroll);
    }
    return () => {
      if (categoryContainer) {
        categoryContainer.removeEventListener('scroll', handleCategoryScroll);
      }
    };
  }, []);

  const categories = [
    {
      name: "OVERSIZED TEES",
      color: "from-violet-600 via-purple-600 to-indigo-600",
      hoverColor: "from-violet-400 via-purple-400 to-indigo-400",
      shadow: "violet-500/40",
      glowColor: "rgba(139, 92, 246, 0.6)",
      icon: Crown,
      route: "/category/oversized-tees",
      image: "https://images.unsplash.com/photo-1576566588028-4147f3842f27?q=80&w=700&auto=format&fit=crop"
    },
    {
      name: "T-SHIRTS",
      color: "from-cyan-600 via-blue-600 to-indigo-600",
      hoverColor: "from-cyan-400 via-blue-400 to-indigo-400",
      shadow: "cyan-500/40",
      glowColor: "rgba(6, 182, 212, 0.6)",
      icon: Star,
      route: "/category/t-shirts",
      image: "https://images.unsplash.com/photo-1618354691438-25bc04584c23?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3",

    },
    {
      name: "HOODIES",
      color: "from-emerald-600 via-teal-600 to-cyan-600",
      hoverColor: "from-emerald-400 via-teal-400 to-cyan-400",
      shadow: "emerald-500/40",
      glowColor: "rgba(16, 185, 129, 0.6)",
      icon: Zap,
      route: "/category/hoodies",
      image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?q=80&w=700&auto=format&fit=crop"
    },
    {
      name: "SHIRTS",
      color: "from-rose-600 via-pink-600 to-purple-600",
      hoverColor: "from-rose-400 via-pink-400 to-purple-400",
      shadow: "rose-500/40",
      glowColor: "rgba(244, 63, 94, 0.6)",
      icon: Award,
      route: "/category/shirts",
      image: "https://images.unsplash.com/photo-1607345366928-199ea26cfe3e?q=80&w=700&auto=format&fit=crop"
    }
  ];

  return (
    <motion.section
      className="pt-8 pb-20 bg-black relative overflow-hidden"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8 }}
      onViewportEnter={() => setIsVisible(true)}
    >


      {/* Enhanced Particles */}
      <motion.div
        className="absolute inset-0 overflow-hidden"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1, delay: 0.3 }}
      >
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-blue-400/15 via-purple-400/15 to-pink-400/15 animate-pulse"
            initial={{ scale: 0, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: i * 0.05 }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </motion.div>

      {/* Floating geometric shapes */}
      <motion.div
        className="absolute inset-0 overflow-hidden pointer-events-none"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1, delay: 0.5 }}
      >
        <motion.div
          className="absolute top-1/4 left-1/4 w-32 h-32 border border-blue-500/10 rounded-full animate-spin"
          style={{animationDuration: '20s'}}
          initial={{ scale: 0, rotate: 0 }}
          whileInView={{ scale: 1, rotate: 360 }}
          viewport={{ once: true }}
          transition={{ duration: 2, delay: 0.6 }}
        />
        <motion.div
          className="absolute top-3/4 right-1/4 w-24 h-24 border border-purple-500/10 rounded-lg rotate-45 animate-pulse"
          initial={{ scale: 0, rotate: 0 }}
          whileInView={{ scale: 1, rotate: 45 }}
          viewport={{ once: true }}
          transition={{ duration: 1.5, delay: 0.8 }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/3 w-16 h-16 border border-pink-500/10 rounded-full animate-bounce"
          style={{animationDuration: '3s'}}
          initial={{ scale: 0, y: 20 }}
          whileInView={{ scale: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 1 }}
        />
      </motion.div>

      <div className="container mx-auto px-6 md:px-12 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 tracking-tight"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            SHOP BY <motion.span
              className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >CATEGORY</motion.span>
          </motion.h2>

          <motion.div
            className="relative max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed font-light">
              Discover your perfect style across our curated collections designed for the modern trendsetter
            </p>
          </motion.div>
        </motion.div>

        {/* Mobile: Horizontal Scroller */}
        <motion.div
          className="block sm:hidden"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="relative">
            <div
              ref={categoryScrollRef}
              className="flex gap-4 overflow-x-auto scrollbar-hide pb-4 px-1 scroll-smooth"
            >
              {categories.map((category, index) => (
                <div
                  key={index}
                  className="flex-none w-80"
                >
                  <Link
                    to={category.route}
                    className="group cursor-pointer relative block"
                  >
                    {/* Full Image Container */}
                    <div className="relative rounded-2xl overflow-hidden h-90 border border-[#2a2a2a] transition-all duration-300 group-hover:border-[#404040]">
                      {/* Background Image */}
                      <img
                        src={category.image}
                        alt={category.name}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />

                      {/* Content Overlay */}
                      <div className="absolute inset-0 flex flex-col justify-end p-6">
                        <div className="space-y-3">
                          <h3 className="text-xl font-bold text-white tracking-wide">
                            {category.name}
                          </h3>
                          <p className="text-sm text-gray-200 opacity-90">
                            Premium Collection
                          </p>
                          <div className="flex items-center justify-between pt-2">
                            <div className="flex items-center text-white/90 text-sm font-medium">
                              <span>Shop Now</span>
                              <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                            </div>
                            <div className="w-8 h-0.5 bg-white/60 rounded-full" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>

            {/* Gradient overlays for scroll indication */}
            {/* <div className="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-gray-900 to-transparent pointer-events-none z-10" />
            <div className="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-gray-900 to-transparent pointer-events-none z-10" /> */}

            {/* Scroll indicator */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex gap-1">
              {categories.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollToCategory(index)}
                  className={`w-1.5 h-1.5 rounded-full transition-all duration-300 cursor-pointer ${
                    index === categoryScrollIndex
                      ? 'bg-[#f5f5f5] scale-125'
                      : 'bg-[#6a6a6a] hover:bg-[#9a9a9a]'
                  }`}
                />
              ))}
            </div>
          </div>
        </motion.div>

        {/* Tablet and Desktop: Horizontal Scroller with Arrows */}
        <motion.div
          className="hidden sm:block"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="relative">
            <div
              ref={categoryScrollRef}
              className="flex gap-6 lg:gap-8 overflow-x-auto scrollbar-hide pb-4 px-1 scroll-smooth"
            >
              {categories.map((category, index) => (
                <motion.div
                  key={index}
                  className="flex-none w-80 lg:w-96"
                  initial={{ opacity: 0, scale: 0.9, y: 20 }}
                  whileInView={{ opacity: 1, scale: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 + index * 0.05 }}
                >
                  <Link
                    to={category.route}
                    className="group cursor-pointer relative block"
                  >
                    {/* Main Card Container */}
                    <div className="relative bg-[#0a0a0a] backdrop-blur-sm rounded-2xl overflow-hidden border border-[#2a2a2a] transition-all duration-300 group-hover:border-[#404040] h-80">
                      {/* Image Container */}
                      <div className="relative h-56 overflow-hidden">
                        <img
                          src={category.image}
                          alt={category.name}
                          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-black/20" />
                      </div>

                      {/* Content */}
                      <div className="p-6">
                        <h3 className="text-lg font-semibold text-white mb-2 tracking-wide">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-400 mb-4">
                          Premium collection
                        </p>
                        <div className="flex items-center text-gray-300 text-sm">
                          <span>Explore</span>
                          <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>

            {/* Desktop Navigation Arrows */}
            <button
              onClick={() => {
                if (categoryScrollRef.current) {
                  categoryScrollRef.current.scrollBy({ left: -400, behavior: 'smooth' });
                }
              }}
              className="absolute left-2 top-1/2 -translate-y-1/2 w-12 h-12 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100 shadow-lg"
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={() => {
                if (categoryScrollRef.current) {
                  categoryScrollRef.current.scrollBy({ left: 400, behavior: 'smooth' });
                }
              }}
              className="absolute right-2 top-1/2 -translate-y-1/2 w-12 h-12 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100 shadow-lg"
            >
              <ChevronRight size={20} />
            </button>

            {/* Gradient overlays for scroll indication */}
            <div className="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-black to-transparent pointer-events-none z-10" />
            <div className="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-black to-transparent pointer-events-none z-10" />
          </div>
        </motion.div>

        {/* Enhanced Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <motion.div
            initial={{ scale: 0.95 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.9 }}
          >
            <Link
              to="/collections"
              className="inline-flex items-center space-x-4 text-gray-400 text-lg font-medium bg-[#1a1a1a] backdrop-blur-lg rounded-full px-8 py-4 shadow-2xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-500 cursor-pointer group relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              <div className="flex space-x-2 relative z-10">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse" style={{animationDelay: '0.6s'}}></div>
              </div>
              <span className="group-hover:text-white transition-colors duration-300 relative z-10 text-sm">Browse all collections</span>
              <ArrowRight size={18} className="opacity-60 group-hover:opacity-100 group-hover:translate-x-2 transition-all duration-300 relative z-10" />
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
};

const WhyChooseUsOptimized = () => {
  const [isVisible, setIsVisible] = useState(false);

  const features = [
    {
      title: "AI-POWERED SUGGESTIONS",
      description: "Smart outfit recommendations powered by advanced AI that learns your style preferences and suggests perfect combinations",
      icon: Zap,
      color: "from-violet-500 via-purple-500 to-indigo-500",
      bgGlow: "violet-500/20",
      glowColor: "rgba(139, 92, 246, 0.6)",
      stats: "Smart AI",
      badge: "NEW"
    },
    {
      title: "CUSTOM OUTFIT BUILDER",
      description: "Create and save unlimited outfit combinations with our intelligent outfit builder that matches colors and styles perfectly",
      icon: Star,
      color: "from-cyan-500 via-blue-500 to-indigo-500",
      bgGlow: "cyan-500/20",
      glowColor: "rgba(6, 182, 212, 0.6)",
      stats: "Unlimited",
      badge: "POPULAR"
    },
    {
      title: "PREMIUM QUALITY",
      description: "Ethically sourced materials and expert craftsmanship in every single piece we create for lasting comfort",
      icon: Shield,
      color: "from-emerald-500 via-teal-500 to-cyan-500",
      bgGlow: "emerald-500/20",
      glowColor: "rgba(16, 185, 129, 0.6)",
      stats: "100% Premium"
    },
    {
      title: "FAST SHIPPING",
      description: "Lightning-fast worldwide delivery with free shipping on orders over $150 and real-time tracking",
      icon: Truck,
      color: "from-blue-500 via-indigo-500 to-purple-500",
      bgGlow: "blue-500/20",
      glowColor: "rgba(59, 130, 246, 0.6)",
      stats: "2-3 Days"
    }
  ];

  return (
    <motion.section
      className="pt-6 pb-12 sm:pt-8 sm:pb-14 md:pb-16 relative overflow-hidden bg-black"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8 }}
      onViewportEnter={() => setIsVisible(true)}
    >

      {/* Floating Elements */}
      <motion.div
        className="absolute inset-0 overflow-hidden"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1, delay: 0.3 }}
      >
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-blue-400/8 via-purple-400/10 to-pink-400/8 animate-pulse"
            initial={{ scale: 0, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: i * 0.03 }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${4 + Math.random() * 3}s`
            }}
          />
        ))}
      </motion.div>

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        {/* Header - Optimized for single line and reduced spacing */}
        <motion.div
          className="text-center mb-6 sm:mb-8"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <motion.h2
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-3 sm:mb-4 text-white whitespace-nowrap"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            WHY CHOOSE <motion.span
              className="text-blue-400"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >WOLFFOXX</motion.span>
          </motion.h2>

          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <p className="text-gray-300 text-sm md:text-base max-w-2xl mx-auto leading-relaxed font-light">
              What sets us apart from the <span className="text-blue-400 font-semibold">competition</span>.
            </p>
            {/* <motion.div
              className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-24 h-0.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full"
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.5 }}
            /> */}
          </motion.div>
        </motion.div>

        {/* Features Grid - Mobile Optimized */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 max-w-7xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <motion.div
                key={index}
                className="group relative"
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.05 }}
              >
                {/* Glow Effect */}
                <div
                  className="absolute -inset-1 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl"
                  style={{
                    background: `linear-gradient(135deg, ${feature.glowColor}, transparent, ${feature.glowColor})`
                  }}
                />

                <div className="relative bg-[#0a0a0a] backdrop-blur-xl p-4 md:p-5 rounded-2xl border border-[#2a2a2a] group-hover:border-[#404040] transition-all duration-300 h-44 md:h-48 transform group-hover:scale-105 shadow-xl group-hover:shadow-purple-500/10">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0" style={{
                      backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 80' width='80' height='80'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='1'%3E%3Cpath d='M20 20h40v40H20z'/%3E%3Cpath d='M40 20v40M20 40h40'/%3E%3C/g%3E%3C/svg%3E")`
                    }} />
                  </div>

                  {/* Stats Badge */}
                  <div className="absolute top-4 right-4 bg-[#1a1a1a] backdrop-blur-sm px-2.5 py-1 rounded-full border border-[#404040]">
                    <span className="text-xs font-bold text-gray-300">
                      {feature.stats}
                    </span>
                  </div>

                  {/* Feature Badge */}
                  {/* {feature.badge && (
                    <div className={`absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-bold ${
                      feature.badge === 'NEW'
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white'
                        : 'bg-gradient-to-r from-orange-500 to-red-500 text-white'
                    } shadow-lg animate-pulse`}>
                      {feature.badge}
                    </div>
                  )} */}

                  {/* Icon */}
                  <div className={`relative w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center mb-2 md:mb-3 group-hover:shadow-xl group-hover:shadow-purple-500/20 transition-all duration-300 transform group-hover:rotate-2 group-hover:scale-110`}>
                    <IconComponent size={18} className="text-white md:w-5 md:h-5" />
                    <div className="absolute inset-0 bg-white/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Icon Glow */}
                    <div
                      className="absolute -inset-1 rounded-2xl opacity-0 group-hover:opacity-40 transition-opacity duration-300 blur-lg"
                      style={{ background: feature.glowColor }}
                    />
                  </div>

                  {/* Content */}
                  <div className="relative z-10">
                    <h3 className="text-sm md:text-base lg:text-lg font-bold text-white mb-1 md:mb-2 tracking-tight leading-tight">
                      {feature.title}
                    </h3>
                    <p className="text-gray-400 text-xs leading-relaxed group-hover:text-gray-300 transition-colors duration-300 font-light mb-1 md:mb-2">
                      {feature.description}
                    </p>

                    {/* Action Indicator */}
                    {/* <div className="flex items-center text-gray-500 group-hover:text-blue-400 transition-colors duration-300">
                      <span className="text-xs font-medium mr-2">LEARN MORE</span>
                      <ArrowRight size={12} className="group-hover:translate-x-1 transition-transform duration-300" />
                    </div> */}
                  </div>

                  {/* Corner Accents */}
                  <div className="absolute top-3 left-3 w-2 h-2 bg-white/20 rounded-full group-hover:bg-white/40 transition-colors duration-300" />
                  <div className="absolute bottom-3 right-3 w-2 h-2 bg-white/20 rounded-full group-hover:bg-white/40 transition-colors duration-300" />
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Bottom Section */}
        {/* <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.9 }}
        > */}
          {/* <motion.div
            className="inline-flex items-center space-x-3 text-gray-400 text-sm font-medium bg-[#1a1a1a] backdrop-blur-sm rounded-full px-6 py-3 shadow-xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-300 cursor-pointer group"
            initial={{ scale: 0.95 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 1 }}
          > */}
            {/* <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-1.5 h-1.5 bg-pink-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div> */}
            {/* <span className="group-hover:text-white transition-colors duration-300">
              Join the WOLFFOXX family today
            </span> */}
            {/* <Users size={14} className="opacity-60 group-hover:opacity-100 transition-opacity duration-300" />
          </motion.div>
        </motion.div> */}
      </div>
    </motion.section>
  );
};

const TestimonialsSectionOptimized = () => {
  const [testimonialScrollIndex, setTestimonialScrollIndex] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const testimonialScrollRef = useRef(null);

  const handleTestimonialScroll = () => {
    if (testimonialScrollRef.current) {
      const container = testimonialScrollRef.current;
      const scrollLeft = container.scrollLeft;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 24; // gap-6
      const totalItemWidth = itemWidth + gap;
      const index = Math.round(scrollLeft / totalItemWidth);
      setTestimonialScrollIndex(Math.min(index, 2)); // 3 testimonials
    }
  };

  const scrollToTestimonial = (index) => {
    if (testimonialScrollRef.current) {
      const container = testimonialScrollRef.current;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 24;
      const totalItemWidth = itemWidth + gap;
      const scrollLeft = index * totalItemWidth;
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    }
  };

  // Enhanced touch/swipe functionality
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartX(e.pageX - testimonialScrollRef.current.offsetLeft);
    setScrollLeft(testimonialScrollRef.current.scrollLeft);
  };

  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartX(e.touches[0].pageX - testimonialScrollRef.current.offsetLeft);
    setScrollLeft(testimonialScrollRef.current.scrollLeft);
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - testimonialScrollRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    testimonialScrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const x = e.touches[0].pageX - testimonialScrollRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    testimonialScrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    const testimonialContainer = testimonialScrollRef.current;
    if (testimonialContainer) {
      testimonialContainer.addEventListener('scroll', handleTestimonialScroll);

      // Mouse events
      testimonialContainer.addEventListener('mousedown', handleMouseDown);
      testimonialContainer.addEventListener('mousemove', handleMouseMove);
      testimonialContainer.addEventListener('mouseup', handleMouseUp);
      testimonialContainer.addEventListener('mouseleave', handleMouseUp);

      // Touch events
      testimonialContainer.addEventListener('touchstart', handleTouchStart);
      testimonialContainer.addEventListener('touchmove', handleTouchMove);
      testimonialContainer.addEventListener('touchend', handleTouchEnd);
    }

    return () => {
      if (testimonialContainer) {
        testimonialContainer.removeEventListener('scroll', handleTestimonialScroll);
        testimonialContainer.removeEventListener('mousedown', handleMouseDown);
        testimonialContainer.removeEventListener('mousemove', handleMouseMove);
        testimonialContainer.removeEventListener('mouseup', handleMouseUp);
        testimonialContainer.removeEventListener('mouseleave', handleMouseUp);
        testimonialContainer.removeEventListener('touchstart', handleTouchStart);
        testimonialContainer.removeEventListener('touchmove', handleTouchMove);
        testimonialContainer.removeEventListener('touchend', handleTouchEnd);
      }
    };
  }, [isDragging, startX, scrollLeft]);

  const testimonials = [
    {
      name: "Alex Rodriguez",
      location: "New York City",
      rating: 5,
      text: "The quality of WOLFFOXX tees is absolutely unmatched. Perfect oversized fit and the fabric gets softer with every wash. These are now my go-to pieces!",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Jordan Martinez",
      location: "Los Angeles",
      rating: 5,
      text: "These designs are fire! I get compliments everywhere I go. The oversized fit is exactly what I was looking for. Worth every single penny and more.",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b75a1f6b?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Taylor Kim",
      location: "London",
      rating: 5,
      text: "Lightning fast shipping and incredible quality. The attention to detail is obvious. I've already ordered three more pieces. WOLFFOXX is the real deal!",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    }
  ];

  return (
    <motion.section
      className="py-12 bg-black relative overflow-hidden"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8 }}
      onViewportEnter={() => setIsVisible(true)}
    >

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-50px" }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-4"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            CUSTOMER LOVE
          </motion.h2>
          <motion.p
            className="text-gray-400 text-lg max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Don't just take our word for it. See what our customers are saying about their WOLFFOXX experience.
          </motion.p>
        </motion.div>

        {/* Mobile: Enhanced Horizontal Scroller */}
        <div className="block md:hidden">
          <div className="relative">
            <div
              ref={testimonialScrollRef}
              className="flex gap-6 overflow-x-auto scrollbar-hide pb-6 px-1 scroll-smooth cursor-grab active:cursor-grabbing"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                WebkitOverflowScrolling: 'touch'
              }}
            >
              {testimonials.map((review, index) => (
                <div key={index} className="flex-none w-80">
                  <div
                    className="group relative h-full transform-gpu transition-all duration-500 hover:scale-105"
                    style={{
                      perspective: '1000px'
                    }}
                  >
                    <div
                      className="relative bg-[#0a0a0a] backdrop-blur-xl p-8 rounded-3xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-500 h-full shadow-2xl"
                      style={{
                        transform: 'rotateX(2deg) rotateY(-2deg)',
                        boxShadow: `
                          0 25px 50px -12px rgba(0, 0, 0, 0.8),
                          0 0 0 1px rgba(255, 255, 255, 0.05),
                          inset 0 1px 0 rgba(255, 255, 255, 0.1)
                        `
                      }}
                    >
                      {/* 3D depth layers */}
                      <div
                        className="absolute inset-0 bg-gradient-to-br from-[#1a1a1a] to-transparent rounded-3xl"
                        style={{
                          transform: 'translateZ(-2px) translateX(-2px) translateY(-2px)'
                        }}
                      />

                      {/* Content */}
                      <div className="relative z-10">
                        {/* Stars */}
                        <div className="flex items-center mb-6 gap-1">
                          {[...Array(review.rating)].map((_, i) => (
                            <Star
                              key={i}
                              size={18}
                              className="text-yellow-400 fill-yellow-400 drop-shadow-sm transform transition-transform duration-300 hover:scale-110"
                            />
                          ))}
                        </div>

                        {/* Quote */}
                        <div className="relative mb-8">
                          <div className="absolute -top-2 -left-2 text-6xl text-slate-600/20 font-serif leading-none">"</div>
                          <p className="text-gray-200 text-base leading-relaxed font-light relative z-10 pl-6">
                            {review.text}
                          </p>
                          <div className="absolute -bottom-4 -right-2 text-6xl text-slate-600/20 font-serif leading-none transform rotate-180">"</div>
                        </div>

                        {/* Profile */}
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <div
                              className="w-14 h-14 rounded-full overflow-hidden border-2 border-[#404040] shadow-lg"
                              style={{
                                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.4)'
                              }}
                            >
                              <img
                                src={review.avatar}
                                alt={review.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-[#0a0a0a] shadow-sm" />
                          </div>
                          <div>
                            <h4 className="text-white font-semibold text-sm mb-1">{review.name}</h4>
                            <p className="text-gray-400 text-xs">{review.location}</p>
                          </div>
                        </div>
                      </div>

                      {/* Shine effect */}
                      <div
                        className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                        style={{
                          background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%)'
                        }}
                      />

                      {/* Corner accent */}
                      <div className="absolute top-6 right-6 w-2 h-2 bg-[#404040] rounded-full group-hover:bg-[#6a6a6a] transition-colors duration-300 shadow-sm" />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Enhanced scroll indicators */}
            <div className="flex justify-center mt-1 gap-3">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollToTestimonial(index)}
                  className={`transition-all duration-300 rounded-full ${
                    index === testimonialScrollIndex
                      ? 'w-8 h-2 bg-[#f5f5f5] shadow-lg'
                      : 'w-2 h-2 bg-[#6a6a6a] hover:bg-[#9a9a9a]'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Desktop: Enhanced 3D Grid */}
        <div className="hidden md:grid md:grid-cols-3 gap-8">
          {testimonials.map((review, index) => (
            <div
              key={index}
              className="group transform-gpu transition-all duration-700 hover:scale-105 hover:-translate-y-2"
              style={{ perspective: '1000px' }}
            >
              <div
                className="relative bg-[#0a0a0a] backdrop-blur-xl p-8 rounded-3xl border border-[#2a2a2a] hover:border-[#404040] transition-all duration-500 h-full shadow-2xl"
                style={{
                  transform: 'rotateX(3deg) rotateY(-3deg)',
                  boxShadow: `
                    0 25px 50px -12px rgba(0, 0, 0, 0.8),
                    0 0 0 1px rgba(255, 255, 255, 0.05),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1)
                  `
                }}
              >
                {/* 3D depth layers */}
                <div
                  className="absolute inset-0 bg-gradient-to-br from-[#1a1a1a] to-transparent rounded-3xl"
                  style={{
                    transform: 'translateZ(-3px) translateX(-3px) translateY(-3px)'
                  }}
                />

                {/* Content */}
                <div className="relative z-10">
                  {/* Stars */}
                  <div className="flex items-center mb-6 gap-1">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star
                        key={i}
                        size={20}
                        className="text-yellow-400 fill-yellow-400 drop-shadow-sm transform transition-transform duration-300 hover:scale-110"
                      />
                    ))}
                  </div>

                  {/* Quote */}
                  <div className="relative mb-8">
                    <div className="absolute -top-4 -left-3 text-7xl text-slate-600/20 font-serif leading-none">"</div>
                    <blockquote className="text-gray-200 text-lg leading-relaxed font-light relative z-10 pl-8 italic">
                      {review.text}
                    </blockquote>
                    <div className="absolute -bottom-6 -right-3 text-7xl text-slate-600/20 font-serif leading-none transform rotate-180">"</div>
                  </div>

                  {/* Profile */}
                  <div className="flex items-center gap-4">
                    <div
                      className="w-14 h-14 rounded-full overflow-hidden border-2 border-[#404040] shadow-lg transition-transform duration-300 hover:scale-110"
                      style={{
                        boxShadow: '0 8px 16px rgba(0, 0, 0, 0.4)'
                      }}
                    >
                      <img
                        src={review.avatar}
                        alt={review.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="text-white font-bold text-lg mb-1">{review.name}</div>
                      <div className="text-gray-400 text-sm">{review.location}</div>
                    </div>
                  </div>
                </div>

                {/* Shine effect */}
                <div
                  className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%)'
                  }}
                />

                {/* Corner accent */}
                <div className="absolute top-6 right-6 w-3 h-3 bg-[#404040] rounded-full group-hover:bg-[#6a6a6a] transition-colors duration-300 shadow-sm" />
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </motion.section>
  );
};

const SubscriptionSectionOptimized = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubscribed(true);
    setTimeout(() => setIsSubscribed(false), 3000);
  };

  return (
    <section className="py-20 bg-[#1a1a1a] relative overflow-hidden">
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='stars' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='10' cy='10' r='0.5' fill='rgba(255,255,255,0.1)'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23stars)'/%3E%3C/svg%3E")`
        }}
      />

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-5xl md:text-7xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6"
            whileHover={{ scale: 1.02 }}
          >
            JOIN THE WOLFFOXX PACK
          </motion.h2>
          <p className="text-blue-200 text-xl mb-12 leading-relaxed">
            Subscribe for exclusive drops, early access to new collections, insider styling tips,
            <br className="hidden md:block" />
            and receive <span className="font-bold text-yellow-400">15% off</span> your first order.
          </p>

          <AnimatePresence mode="wait">
            {!isSubscribed ? (
              <motion.form
                onSubmit={handleSubmit}
                className="flex flex-col md:flex-row gap-4 max-w-2xl mx-auto mb-8"
                initial={{ opacity: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.3 }}
              >
                <motion.input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="flex-1 px-6 py-4 rounded-xl border-2 border-[#404040] bg-[#0a0a0a] backdrop-blur-sm text-white placeholder-[#6a6a6a] focus:outline-none focus:ring-2 focus:ring-[#6a6a6a] focus:border-[#6a6a6a] transition-all"
                  required
                  whileFocus={{ scale: 1.02 }}
                />
                <motion.button
                  type="submit"
                  className="px-8 py-4 bg-[#FF6B35] text-white font-bold rounded-xl hover:bg-[#ff8c00] transition-all duration-300 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  SUBSCRIBE NOW
                </motion.button>
              </motion.form>
            ) : (
              <motion.div
                className="max-w-2xl mx-auto mb-8"
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6 backdrop-blur-sm">
                  <motion.div
                    className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", delay: 0.2 }}
                  >
                    <motion.svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="white"
                      strokeWidth="3"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                    >
                      <path d="M20 6L9 17l-5-5" />
                    </motion.svg>
                  </motion.div>
                  <h3 className="text-2xl font-bold text-white mb-2">Welcome to the Pack! 🐺</h3>
                  <p className="text-green-200">Check your email for your exclusive 15% off code!</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <p className="text-blue-300 text-sm max-w-2xl mx-auto leading-relaxed">
            By subscribing, you agree to receive marketing emails from WOLFFOXX.
            You can unsubscribe at any time. We respect your privacy and will never share your information.
          </p>

          {/* Social Proof */}
          <motion.div
            className="flex items-center justify-center gap-8 mt-12 text-blue-200"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5 }}
          >
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-white">25K+</span>
              <span className="text-sm">Subscribers</span>
            </div>
            <div className="w-px h-8 bg-[#404040]" />
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-white">4.9★</span>
              <span className="text-sm">Rating</span>
            </div>
            <div className="w-px h-8 bg-[#404040]" />
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-white">95%</span>
              <span className="text-sm">Satisfaction</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

const InstagramFeedOptimized = () => {
  const instagramPosts = [
    "https://images.unsplash.com/photo-1550123297-69e7c3b3e9d1?q=80&w=400&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1551833726-b6e7210484c2?q=80&w=400&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1499939667766-4afceb292d05?q=80&w=400&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1536243298747-ea8874136d64?q=80&w=400&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1507680434567-5739c80be1ac?q=80&w=400&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1516826957135-700dedea698c?q=80&w=400&auto=format&fit=crop"
  ];

  return (
    <section className="py-24 bg-black relative overflow-hidden">

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-5xl md:text-6xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6"
            whileHover={{ scale: 1.02 }}
          >
            @WOLFFOXX
          </motion.h2>
          <p className="text-gray-400 text-lg max-w-3xl mx-auto mb-8">
            Follow us on Instagram for daily styling inspiration, behind-the-scenes content,
            and exclusive sneak peeks of upcoming drops.
          </p>

          <motion.a
            href="https://www.instagram.com/wolffoxx.in?igsh=NWVvdjJ0cWo0dzE0"
            target="_blank"
            rel="noopener noreferrer"
className="inline-flex items-center gap-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white px-8 py-4 rounded-full font-bold transition-all duration-300 shadow-lg hover:shadow-xl"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
            Follow @WOLFFOXX
          </motion.a>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {instagramPosts.map((post, index) => (
            <motion.div
              key={index}
              className="group relative aspect-square overflow-hidden rounded-2xl cursor-pointer"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.05, y: -5 }}
            >
              <img
                src={post}
                alt={`Instagram post ${index + 1}`}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                loading="lazy"
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-[#1a1a1a]/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex items-center justify-between text-white text-sm">
                    <div className="flex items-center gap-2">
                      <Heart size={16} className="fill-red-500 text-red-500" />
                      <span>{Math.floor(Math.random() * 500) + 100}</span>
                    </div>
                    <ArrowUpRight size={16} />
                  </div>
                </div>
              </div>

              {/* Instagram icon overlay */}
              <div className="absolute top-4 right-4 w-8 h-8 bg-[#f5f5f5] backdrop-blur-sm rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg className="w-4 h-4 text-pink-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </div>
            </motion.div>
          ))}
        </div>

        {/* User Generated Content Section */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <div className="bg-[#0a0a0a] backdrop-blur-sm rounded-2xl p-4 border border-[#2a2a2a]">
            <h3 className="text-2xl font-bold text-white mb-4">Show Off Your Style</h3>
            <p className="text-gray-400 mb-6">
              Tag us <span className="text-blue-400 font-medium">@wolffoxx</span> and
              use <span className="text-[#FF6B35] font-medium">#WolffoxxStyle</span> for a chance to be featured!
            </p>
            {/* <div className="flex flex-wrap justify-center gap-3">
              <span className="px-4 py-2 bg-[#2a2a2a] text-[#FF6B35] rounded-full text-sm font-medium">
                #WolffoxxStyle
              </span>
              <span className="px-4 py-2 bg-[#2a2a2a] text-[#FF6B35] rounded-full text-sm font-medium">
                #OversizedVibes
              </span>
              <span className="px-4 py-2 bg-[#2a2a2a] text-[#FF6B35] rounded-full text-sm font-medium">
                #StreetStyle
              </span>
            </div> */}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// NEW FAQ Section Component
const FAQSection = () => {
  const [openFAQ, setOpenFAQ] = useState(null);

  const faqs = [
    {
      question: "What makes WOLFFOXX tees different from regular oversized tees?",
      answer: "Our tees are specifically designed with dropped shoulders, extended length, and premium cotton blends that maintain their shape wash after wash. Each piece features unique graphics and superior construction that sets them apart from mass-market alternatives."
    },
    {
      question: "How do I choose the right size for an oversized fit?",
      answer: "Our oversized tees are designed to be worn 1-2 sizes larger than your regular fit. For the perfect oversized look, we recommend sizing up from your usual size. Check our detailed size guide for specific measurements and fit recommendations."
    },
    {
      question: "What's your return and exchange policy?",
      answer: "We offer a 30-day return policy for unworn items with original tags. Exchanges are free within the US, and we also accept returns for store credit. International returns are accepted but shipping costs apply."
    },
    {
      question: "How long does shipping take?",
      answer: "Domestic orders typically arrive within 3-5 business days with standard shipping. Express shipping (1-2 days) is available. International orders take 7-14 business days. Free shipping is included on all orders over $150."
    },
    {
      question: "Are your products sustainably made?",
      answer: "Yes! We're committed to ethical production. Our cotton is sustainably sourced, and we work with certified factories that maintain fair labor practices. We also use eco-friendly packaging and offer a clothing recycling program."
    },
    {
      question: "Do you restock sold-out items?",
      answer: "Popular items are typically restocked within 4-6 weeks. Limited edition pieces may not be restocked. Sign up for restock notifications on product pages, or follow our social media for updates on new drops and restocks."
    },
    {
      question: "How should I care for my WOLFFOXX tees?",
      answer: "Machine wash cold with like colors, tumble dry low or hang dry. Avoid bleach and high heat. Our premium cotton actually gets softer with each wash when cared for properly. Iron inside-out if needed to preserve graphics."
    },
    {
      question: "Do you offer wholesale or bulk orders?",
      answer: "Yes, we offer wholesale pricing for qualified retailers and bulk orders for organizations. Minimum order quantities apply. Contact our wholesale <NAME_EMAIL> for pricing and requirements."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <section className="pt-6 pb-12 bg-black relative overflow-hidden">

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.h2
            className="text-5xl md:text-7xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6"
            whileHover={{ scale: 1.02 }}
          >
            FREQUENTLY ASKED QUESTIONS
          </motion.h2>
          <p className="text-gray-400 text-lg max-w-3xl mx-auto leading-relaxed">
            Got questions? We've got answers. Find everything you need to know about
            WOLFFOXX products, shipping, sizing, and more.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                className="bg-[#0a0a0a] backdrop-blur-sm rounded-2xl border border-[#2a2a2a] overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <motion.button
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-[#1a1a1a] transition-colors duration-300"
                  onClick={() => toggleFAQ(index)}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <h3 className="text-white font-bold text-lg pr-4 leading-relaxed">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openFAQ === index ? 45 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <Plus
                      size={24}
                      className={`transition-colors duration-300 ${
                        openFAQ === index ? 'text-[#FF6B35]' : 'text-gray-400'
                      }`}
                    />
                  </motion.div>
                </motion.button>

                <AnimatePresence>
                  {openFAQ === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6">
                        <motion.div
                          initial={{ y: -10, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ duration: 0.3, delay: 0.1 }}
                          className="border-t border-[#2a2a2a] pt-4"
                        >
                          <p className="text-gray-300 leading-relaxed text-base">
                            {faq.answer}
                          </p>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          {/* Contact Support Section */}
          <motion.div
            className="mt-8 text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5 }}
          >
            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-2xl p-6 border border-[#2a2a2a]">
              <h3 className="text-xl font-bold text-[#f5f5f5] mb-3">Still have questions?</h3>
              <p className="text-[#9a9a9a] mb-4 max-w-2xl mx-auto">
                Our customer support team is here to help! Reach out via email, live chat,
                or social media for personalized assistance.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <motion.a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center gap-2 bg-[#333333] hover:bg-[#404040] text-[#FFFFFF] px-6 py-3 rounded-lg font-medium transition-colors border border-[#555555]"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Email Support
                  <ArrowUpRight size={16} />
                </motion.a>
                <motion.button
                  className="inline-flex items-center gap-2 bg-[#FF6B35] hover:bg-[#e55a2b] text-[#FFFFFF] px-6 py-3 rounded-lg font-medium transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Live Chat
                  <ArrowUpRight size={16} />
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

// BestSellerCard Component - Styled like FeaturedCollection
function BestSellerCard({ product, index, isVisible }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);

  // Get current images based on selected color
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();

  const nextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
  };

  const prevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
  };

  const goToImage = (idx, e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex(idx);
  };

  const handleColorSelect = (idx) => {
    setSelectedColorIndex(idx);
    // Reset to first image when color changes
    setCurrentImageIndex(0);
  };



  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-[#0a0a0a] rounded-xl overflow-hidden group border border-[#2a2a2a]"
    >
      <Link to={`/product/${product.id}`} className="block relative">
        {/* Image Carousel Container */}
        <div className="h-72 md:h-80 overflow-hidden relative">
          <div className="relative w-full h-full">
            {currentImages.map((image, idx) => (
              <img
                key={idx}
                src={image}
                alt={`${product.name} - ${product.colors[selectedColorIndex]?.name || 'Default'} - Image ${idx + 1}`}
                loading="lazy"
                className={`absolute inset-0 w-full h-full object-cover transform group-hover:scale-105 transition-all duration-700 ease-in-out ${
                  idx === currentImageIndex
                    ? 'opacity-100 z-10'
                    : 'opacity-0 z-0'
                }`}
                onError={(e) => {
                  e.target.src = '/api/placeholder/400/400';
                }}
              />
            ))}
          </div>

          {/* Navigation Arrows - Always visible */}
          {currentImages.length > 1 && (
            <>
              <button
                onClick={prevImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronLeft size={16} />
              </button>
              <button
                onClick={nextImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronRight size={16} />
              </button>
            </>
          )}

          {/* Image Dots Indicator */}
          {currentImages.length > 1 && (
            <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-1.5 z-20">
              {currentImages.map((_, idx) => (
                <button
                  key={idx}
                  onClick={(e) => goToImage(idx, e)}
                  onMouseDown={(e) => e.stopPropagation()}
                  onTouchStart={(e) => e.stopPropagation()}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    idx === currentImageIndex
                      ? 'bg-white scale-110'
                      : 'bg-white/50 hover:bg-white/75'
                  }`}
                />
              ))}
            </div>
          )}

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
          {product.isNew && (
            <div className="bg-[#6a6a6a] text-[#f5f5f5] text-xs font-medium px-2 py-1 rounded-md shadow-lg">
              NEW
            </div>
          )}
          {product.salePrice && (
            <div className="bg-[#404040] text-[#f5f5f5] text-xs font-medium px-2 py-1 rounded-md shadow-lg">
              SALE
            </div>
          )}
        </div>

        {/* Image Counter */}
        {currentImages.length > 1 && (
          <div className="absolute top-3 right-12 bg-black/50 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm">
            {currentImageIndex + 1}/{currentImages.length}
          </div>
        )}

        {/* Wishlist Button */}
        <div className="absolute top-3 right-3 z-10">
          <WishlistButton
            productId={product.id}
            productName={product.name}
            productPrice={product.salePrice || product.price}
            productImage={currentImages[0] || product.images?.[0]}
            className="bg-[#2a2a2a] hover:bg-[#404040] text-[#d4d4d4] w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200"
          />
        </div>

        {/* Quick Actions Overlay */}
        {/* <BestSellerAnimatedInfo product={product} isVisible={isHovered} /> */}
      </Link>

      {/* Product Info */}
      <div className="p-4 md:p-5">
        <div className="mb-3">
          <span className="text-[#6a6a6a] text-xs font-normal uppercase tracking-wider opacity-80">
            {product.category}
          </span>
          <h3 className="text-[#d4d4d4] font-semibold text-lg hover:text-[#9a9a9a] transition-colors mt-1 line-clamp-1 truncate">
            <Link to={`/product/${product.id}`} className="block truncate" title={product.name}>{product.name}</Link>
          </h3>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {product.salePrice ? (
              <>
                <span className="text-[#f5f5f5] font-bold text-lg">${product.salePrice}</span>
                <span className="text-[#6a6a6a] line-through text-sm">${product.price}</span>
                <span className="text-xs bg-[#404040] text-[#d4d4d4] px-2 py-1 rounded-full font-medium">
                  {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                </span>
              </>
            ) : (
              <span className="text-[#f5f5f5] font-bold text-lg">${product.price}</span>
            )}
          </div>
        </div>

        {/* Color Selection */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex gap-1.5">
              {product.colors.slice(0, 4).map((color, idx) => (
                <button
                  key={idx}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleColorSelect(idx);
                  }}
                  className={`w-5 h-5 rounded-full border-2 transition-all duration-200 ${
                    selectedColorIndex === idx
                      ? 'border-[#f5f5f5] scale-110 shadow-lg shadow-white/25'
                      : 'border-[#404040] hover:border-[#6a6a6a]'
                  }`}
                  style={{ backgroundColor: color.value }}
                  title={color.name}
                />
              ))}
              {product.colors.length > 4 && (
                <div className="w-5 h-5 rounded-full bg-[#2a2a2a] flex items-center justify-center text-xs text-[#9a9a9a] font-medium">
                  +{product.colors.length - 4}
                </div>
              )}
            </div>
          </div>

          {/* Stock Status */}
          <div className="text-xs text-[#6a6a6a]">
            {product.stock > 0 ? (
              <span className="text-[#9a9a9a]">● In Stock</span>
            ) : (
              <span className="text-[#9a9a9a]">● In Stock</span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

// function BestSellerAnimatedInfo({ product, isVisible }) {
//   return (
//     <motion.div
//       initial={false}
//       animate={{
//         opacity: isVisible ? 1 : 0,
//         y: isVisible ? 0 : 20,
//       }}
//       transition={{ duration: 0.2 }}
//       className="absolute inset-x-0 bottom-0 p-4 md:p-5 bg-gradient-to-t from-gray-900/95 via-gray-900/80 to-transparent backdrop-blur-sm"
//     >
//       <div className="flex items-center justify-between gap-3">
//         <div className="flex-1">
//           <div className="text-white font-medium text-sm">{product.name}</div>
//           <div className="text-gray-300 text-xs">{product.category}</div>
//         </div>

//         <div className="flex gap-2">
//           <Link
//             to={`/product/${product.id}`}
//             className="bg-white/10 hover:bg-white/20 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-all duration-200 backdrop-blur-sm border border-white/20"
//           >
//             <Eye size={12} />
//             Quick View
//           </Link>
//           <button className="bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-colors">
//             <ShoppingBag size={12} />
//             Add to Cart
//           </button>
//         </div>
//       </div>
//     </motion.div>
//   );
// }