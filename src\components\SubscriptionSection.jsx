import { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, Mail } from 'lucide-react';

export default function SubscriptionSection() {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    if (!email) {
      setError('Please enter your email');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsSubscribed(true);
      // Reset form
      setEmail('');
    }, 1500);
  };

  return (
    <section className="py-16 bg-gradient-to-br from-[#06b6d4] to-[#3b82f6] overflow-hidden relative">
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <div className="max-w-3xl mx-auto">
          <motion.div
            className="bg-[#1e293b] backdrop-blur-sm border border-white/10 rounded-xl p-6 sm:p-8 md:p-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-8">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                viewport={{ once: true }}
                className="inline-flex items-center justify-center w-16 h-16 bg-[#3b82f6] rounded-full mb-4"
              >
                <Mail size={32} className="text-white" />
              </motion.div>

              <h2 className="text-2xl sm:text-3xl md:text-4xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-3">
                GET 15% OFF YOUR FIRST ORDER
              </h2>
              <p className="text-[#94a3b8] text-sm sm:text-base px-2 sm:px-0">
                Subscribe for exclusive drops, early access, and insider styling tips
              </p>
            </div>

            {!isSubscribed ? (
              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
                <div className="flex-grow relative">
                  <input
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className={`w-full py-3 px-4 bg-[#1e293b] border ${
                      error ? 'border-red-500' : 'border-[#475569] focus:border-[#3b82f6]'
                    } rounded-md text-[#e2e8f0] outline-none transition-colors text-sm sm:text-base`}
                  />
                  {error && <p className="text-[#ec4899] text-xs sm:text-sm mt-1 absolute">{error}</p>}
                </div>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={isLoading}
                  className={`py-3 px-6 bg-[#FFA500] text-black font-medium rounded-md transition-colors flex-shrink-0 flex items-center justify-center min-w-[120px] text-sm sm:text-base ${
                    isLoading ? 'bg-[#475569]' : 'hover:bg-[#ff8c00]'
                  }`}
                >
                  {isLoading ? (
                    <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin" />
                  ) : (
                    'Subscribe Now'
                  )}
                </motion.button>
              </form>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center p-4 bg-[#10b981]/10 border border-[#10b981]/30 rounded-md"
              >
                <div className="flex items-center justify-center gap-2 text-[#10b981] mb-1">
                  <Check size={20} />
                  <span className="font-medium">Successfully subscribed!</span>
                </div>
                <p className="text-[#e2e8f0] text-sm">
                  Thank you for subscribing. Check your email for your exclusive discount code!
                </p>
              </motion.div>
            )}

            <div className="mt-8 pt-6 md:pt-8 border-t border-[#475569]">
              <div className="grid grid-cols-3 gap-3 sm:gap-4 md:flex md:items-center md:justify-center md:gap-8">
                <div className="text-center">
                  <div className="text-white font-bold text-lg sm:text-xl md:text-2xl">25K+</div>
                  <div className="text-[#94a3b8] text-xs sm:text-sm">Subscribers</div>
                </div>
                <div className="text-center">
                  <div className="text-white font-bold text-lg sm:text-xl md:text-2xl">4.9★</div>
                  <div className="text-[#94a3b8] text-xs sm:text-sm">Reviews</div>
                </div>
                <div className="text-center">
                  <div className="text-white font-bold text-lg sm:text-xl md:text-2xl">95%</div>
                  <div className="text-[#94a3b8] text-xs sm:text-sm">Satisfaction</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute top-1/4 -left-20 w-40 h-40 bg-white/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 -right-20 w-60 h-60 bg-white/10 rounded-full blur-3xl"></div>
    </section>
  );
}