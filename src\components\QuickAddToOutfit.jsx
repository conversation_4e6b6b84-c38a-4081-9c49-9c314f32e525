import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X, Shirt, Save, Sparkles, Check, Trash2, Heart, Star, ShoppingBag, Palette, Wand2 } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useOutfit } from '../context/OutfitContext';
import { Link } from 'react-router-dom';
import SmartColorMatching from './SmartColorMatching';

const QuickAddToOutfit = ({ currentProduct, selectedColor }) => {
  const [isOutfitBuilderOpen, setIsOutfitBuilderOpen] = useState(false);
  const [currentOutfit, setCurrentOutfit] = useState([]);
  const [outfitName, setOutfitName] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [matchingItems, setMatchingItems] = useState([]);
  const { addToCart } = useCart();
  const { saveOutfit: saveOutfitToContext } = useOutfit();

  // Add current product to outfit when component mounts or color changes
  useEffect(() => {
    if (currentProduct) {
      if (currentOutfit.length === 0) {
        // Add product for the first time
        setCurrentOutfit([{
          ...currentProduct,
          selectedColor: selectedColor || currentProduct.colors?.[0] || null,
          selectedSize: currentProduct.sizes?.[0] || 'M',
          category: currentProduct.category
        }]);
      } else {
        // Update existing product's color if it's the current product
        setCurrentOutfit(prev => prev.map(item =>
          item.id === currentProduct.id
            ? { ...item, selectedColor: selectedColor || currentProduct.colors?.[0] || null }
            : item
        ));
      }
    }
  }, [currentProduct, selectedColor]);

  // Handler for adding items from smart matching
  const handleAddToOutfit = useCallback((item) => {
    const isAlreadyInOutfit = currentOutfit.some(outfitItem => outfitItem.id === item.id);
    if (!isAlreadyInOutfit && currentOutfit.length < 5) {
      setCurrentOutfit(prev => [...prev, {
        ...item,
        selectedColor: item.matchingColor || item.colors?.[0] || null,
        selectedSize: item.sizes?.[0] || 'M',
        category: item.category,
        matchingColor: item.matchingColor,
        matchScore: item.matchScore,
        matchReason: item.matchReason
      }]);
    }
  }, [currentOutfit]);

  // Handler for updating matching items from SmartColorMatching
  const handleMatchingItemsUpdate = useCallback((items) => {
    setMatchingItems(items);
  }, []);

  const addToOutfit = (product) => {
    const isAlreadyInOutfit = currentOutfit.some(item => item.id === product.id);
    if (!isAlreadyInOutfit && currentOutfit.length < 5) {
      setCurrentOutfit(prev => [...prev, {
        ...product,
        selectedColor: product.matchingColor || product.colors?.[0] || null,
        selectedSize: product.sizes?.[0] || 'M',
        category: product.category,
        matchingColor: product.matchingColor, // Store the matching color info
        matchScore: product.matchScore,
        matchReason: product.matchReason
      }]);
    }
  };

  const removeFromOutfit = (productId) => {
    setCurrentOutfit(prev => prev.filter(item => item.id !== productId));
  };

  const saveOutfit = () => {
    if (currentOutfit.length === 0 || !outfitName.trim()) return;

    // Use the OutfitContext's saveOutfit function
    saveOutfitToContext(currentOutfit, outfitName.trim());

    setSuccessMessage('Outfit saved successfully! ✨');
    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
      setIsOutfitBuilderOpen(false);
      setOutfitName('');
      setCurrentOutfit([]);
    }, 2000);
  };

  const addOutfitToCart = () => {
    if (currentOutfit.length === 0) return;

    const outfitNameForCart = outfitName || `Outfit ${Date.now()}`;
    const baseOperationId = `quick-outfit-${Date.now()}`;

    console.log('🛒 OUTFIT TO CART - Starting process:', {
      outfitName: outfitNameForCart,
      itemCount: currentOutfit.length,
      baseOperationId,
      items: currentOutfit.map(item => ({ id: item.id, name: item.name }))
    });

    // Add a small delay between each item to prevent race conditions
    currentOutfit.forEach((item, index) => {
      setTimeout(() => {
        // Use the selected color or matching color
        const selectedColor = item.selectedColor || item.matchingColor || item.colors?.[0];

        // Create unique operation ID for each item
        const itemOperationId = `${baseOperationId}-item-${item.id}-${index}`;

        // Format the product correctly for the cart
        const cartProduct = {
          id: item.id,
          name: item.name,
          price: item.salePrice || item.price,
          color: selectedColor?.name || 'Default',
          size: item.selectedSize || item.sizes?.[0] || 'M',
          image: selectedColor?.images?.[0] || item.images?.[0] || item.image,
          outfitName: outfitNameForCart, // Group items under outfit name
          category: item.category,
          operationId: itemOperationId // Add unique operation ID for each item
        };

        console.log(`🛒 OUTFIT TO CART - Adding item ${index + 1}/${currentOutfit.length}:`, cartProduct);
        addToCart(cartProduct, 1); // Add 1 quantity of each item
      }, index * 50); // 50ms delay between each item
    });

    setSuccessMessage(`Outfit added to cart successfully! 🛒 (${currentOutfit.length} items)`);
    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
      setIsOutfitBuilderOpen(false);
      setOutfitName('');
      setCurrentOutfit([]);
    }, 2000);
  };

  const getTotalPrice = () => {
    return currentOutfit.reduce((total, item) => total + (item.salePrice || item.price), 0).toFixed(2);
  };

  const getOutfitCategories = () => {
    const categories = [...new Set(currentOutfit.map(item => item.category))];
    return categories.join(', ');
  };

  return (
    <>
      {/* Outfit Builder Button */}
      <motion.button
        onClick={() => setIsOutfitBuilderOpen(true)}
        className="w-full text-white py-3 px-6 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
        style={{
          backgroundColor: 'rgba(192, 132, 252, 0.2)', // #c084fc with transparency
          border: '1px solid rgba(192, 132, 252, 0.3)',
          boxShadow: '0 0 20px rgba(192, 132, 252, 0.15)'
        }}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = 'rgba(192, 132, 252, 0.3)';
          e.target.style.boxShadow = '0 0 25px rgba(192, 132, 252, 0.25)';
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = 'rgba(192, 132, 252, 0.2)';
          e.target.style.boxShadow = '0 0 20px rgba(192, 132, 252, 0.15)';
        }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Wand2 size={20} style={{ color: '#c084fc' }} />
        <span style={{ color: '#c084fc' }}>Build Complete Outfit</span>
      </motion.button>

      {/* Outfit Builder Modal */}
      <AnimatePresence>
        {isOutfitBuilderOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
              onClick={() => setIsOutfitBuilderOpen(false)}
            />

            {/* Modal - Responsive sizing - Fixed desktop sizing */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="fixed top-4 left-4 right-4 bottom-4 sm:top-8 sm:left-8 sm:right-8 sm:bottom-8 md:top-12 md:left-[20%] md:right-[20%] md:bottom-12 lg:top-16 lg:left-[25%] lg:right-[25%] lg:bottom-16 xl:left-[30%] xl:right-[30%] bg-[#1a1a1a] backdrop-blur-xl border border-[#404040] rounded-xl z-50 flex flex-col"
              style={{ overflow: 'hidden', maxHeight: 'calc(100vh - 8rem)' }}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 lg:p-6 border-b border-[#2a2a2a] flex-shrink-0 bg-[#1a1a1a] shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2.5 rounded-lg shadow-md bg-gradient-to-br from-[#FF6B35] to-[#F7931E]">
                    <Shirt size={20} className="text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl lg:text-2xl font-bold text-white tracking-tight">Outfit Builder</h2>
                    <p className="text-[#AAAAAA] text-sm font-medium">Create and save your perfect look</p>
                  </div>
                </div>
                <button
                  onClick={() => setIsOutfitBuilderOpen(false)}
                  className="p-2.5 text-[#AAAAAA] hover:text-white transition-colors rounded-lg hover:bg-[#2a2a2a] border border-[#404040] hover:border-[#6a6a6a]"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4 lg:p-6">
                {/* Current Outfit */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                    <Shirt size={18} />
                    Your Outfit
                    <span className="bg-[#404040] text-[#AAAAAA] text-xs px-2 py-1 rounded-full">
                      {currentOutfit.length}/5 items
                    </span>
                  </h3>

                  {/* Responsive grid - more columns on desktop */}
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 lg:gap-4">
                    {currentOutfit.map((item, index) => {
                      const isCurrentProduct = item.id === currentProduct?.id;
                      return (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.1 }}
                          className={`relative bg-[#2a2a2a] rounded-lg border transition-all duration-300 overflow-hidden ${
                            isCurrentProduct
                              ? 'border-blue-500/50 bg-blue-900/20'
                              : 'border-[#404040] hover:border-[#6a6a6a]'
                          }`}
                        >
                          {/* Current Product Badge */}
                          {isCurrentProduct && (
                            <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1 z-10">
                              <Star size={10} />
                              Current
                            </div>
                          )}

                          {/* Match Score Badge */}
                          {item.matchScore && (
                            <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                              {item.matchScore}%
                            </div>
                          )}

                          <div className="p-3">
                            <Link to={`/product/${item.id}`} className="block">
                              <img
                                src={item.selectedColor?.images?.[0] || item.matchingColor?.images?.[0] || item.images[0]}
                                alt={item.name}
                                className="w-full aspect-square object-cover rounded-lg mb-3 hover:scale-105 transition-transform duration-300"
                              />
                              <h4 className="text-white text-sm font-medium line-clamp-2 mb-2 hover:text-cyan-400 transition-colors">
                                {item.name}
                              </h4>
                            </Link>
                            <p className="text-[#AAAAAA] text-xs mb-2">{item.category}</p>

                            {/* Match Reason */}
                            {item.matchReason && (
                              <div className="text-green-400 text-xs mb-3 flex items-center gap-2">
                                <div
                                  className="w-3 h-3 rounded-full border border-white/20 flex-shrink-0"
                                  style={{ backgroundColor: (item.selectedColor || item.matchingColor)?.hex || '#10b981' }}
                                ></div>
                                <span className="line-clamp-1">{item.matchReason}</span>
                              </div>
                            )}

                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-2">
                                {item.salePrice ? (
                                  <>
                                    <span className="text-red-400 font-semibold text-sm">${item.salePrice}</span>
                                    <span className="text-slate-500 line-through text-xs">${item.price}</span>
                                  </>
                                ) : (
                                  <span className="text-cyan-400 font-semibold text-sm">${item.price}</span>
                                )}
                              </div>
                              {!isCurrentProduct && currentOutfit.length > 1 && (
                                <button
                                  onClick={() => removeFromOutfit(item.id)}
                                  className="p-1 text-[#AAAAAA] hover:text-red-400 transition-colors"
                                >
                                  <Trash2 size={14} />
                                </button>
                              )}
                            </div>

                            {/* Color Indicator */}
                            {(item.selectedColor || item.matchingColor) && (
                              <div className="flex items-center gap-2">
                                <div
                                  className="w-4 h-4 rounded-full border-2 border-white/30 flex-shrink-0"
                                  style={{ backgroundColor: (item.selectedColor || item.matchingColor)?.hex || (item.selectedColor || item.matchingColor)?.value }}
                                  title={`Color: ${(item.selectedColor || item.matchingColor)?.name}`}
                                />
                                <span className="text-[#AAAAAA] text-xs truncate">
                                  {(item.selectedColor || item.matchingColor)?.name}
                                </span>
                              </div>
                            )}
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>

                {/* Smart Style Matching */}
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Palette size={20} className="text-blue-400" />
                    <h3 className="text-lg font-semibold text-white">Smart Style Matching</h3>
                    <Sparkles size={16} className="text-blue-400" />
                  </div>

                  {/* Base Color Indicator */}
                  {(selectedColor || currentProduct?.colors?.[0]) && (
                    <div className="flex items-center gap-2 mb-4 text-sm">
                      <span className="text-[#AAAAAA]">Matching with:</span>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded-full border border-white/30"
                          style={{ backgroundColor: (selectedColor || currentProduct.colors?.[0])?.hex || (selectedColor || currentProduct.colors?.[0])?.value }}
                          title={`Base color: ${(selectedColor || currentProduct.colors?.[0])?.name}`}
                        />
                        <span className="text-blue-400 font-medium">
                          {(selectedColor || currentProduct.colors?.[0])?.name}
                        </span>
                      </div>
                    </div>
                  )}

                  <SmartColorMatching
                    currentProduct={currentProduct}
                    selectedColor={selectedColor}
                    onAddToOutfit={handleAddToOutfit}
                    onMatchingItemsUpdate={handleMatchingItemsUpdate}
                    isInModal={true}
                  />
                </div>
              </div>

              {/* Footer */}
              <div className="border-t border-[#2a2a2a] p-4 lg:p-6 flex-shrink-0">
                <div className="flex flex-col gap-4">
                  <div className="space-y-2">
                    <input
                      type="text"
                      placeholder="Name your outfit (e.g., 'Summer Casual') *"
                      value={outfitName}
                      onChange={(e) => setOutfitName(e.target.value)}
                      className={`w-full bg-[#2a2a2a] border rounded-lg px-4 py-3 text-white placeholder-[#AAAAAA] focus:outline-none transition-colors ${
                        outfitName.trim()
                          ? 'border-[#404040] focus:border-blue-500'
                          : 'border-red-500/50 focus:border-red-500'
                      }`}
                      required
                    />
                    {!outfitName.trim() && (
                      <p className="text-red-400 text-sm flex items-center gap-1">
                        <span>*</span>
                        Outfit name is required to save
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <motion.button
                      onClick={saveOutfit}
                      disabled={currentOutfit.length === 0 || !outfitName.trim()}
                      className="flex-1 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{
                        background: (currentOutfit.length === 0 || !outfitName.trim()) ? '#64748b' : 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                      }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Save size={18} />
                      Save Outfit
                    </motion.button>

                    <motion.button
                      onClick={addOutfitToCart}
                      disabled={currentOutfit.length === 0}
                      className="flex-1 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{
                        background: currentOutfit.length === 0 ? '#64748b' : 'linear-gradient(to bottom right, #FF6B35 0%, #F7931E 100%)'
                      }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <ShoppingBag size={18} />
                      Add to Cart (${getTotalPrice()})
                    </motion.button>
                  </div>
                </div>

                {/* Outfit Info */}
                <div className="mt-4 text-center">
                  <span className="text-[#AAAAAA] text-sm">
                    {currentOutfit.length} item{currentOutfit.length !== 1 ? 's' : ''} • Total: ${getTotalPrice()}
                  </span>
                </div>
              </div>

              {/* Success Message */}
              <AnimatePresence>
                {showSuccess && (
                  <motion.div
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 50 }}
                    className="absolute bottom-6 left-6 right-6 bg-green-500 text-white p-4 rounded-xl flex items-center gap-3"
                  >
                    {successMessage.includes('saved') ? <Save size={20} /> : <ShoppingBag size={20} />}
                    <span className="font-medium">{successMessage}</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default QuickAddToOutfit;