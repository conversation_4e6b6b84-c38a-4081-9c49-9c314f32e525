import React, { useState, useEffect, useRef } from 'react';
import { Search, TrendingUp, ArrowLeft, Clock, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { dataService } from '../services/dataService';
import ProductCard from '../components/ProductCard';

const topSearches = ['oversized tees', 't-shirts', 'hoodies', 'shirts', 'baggy jeans', 'jackets'];
const colorSearchSuggestions = ['black hoodie', 'white shirt', 'blue jeans', 'gray tee', 'navy jacket', 'red shirt'];

export default function SearchPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [recentSearches, setRecentSearches] = useState(['Hoodies', 'Winter jackets', 'Premium tees']);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [showQuickSearch, setShowQuickSearch] = useState(false);
  const [selectedTrendingCategory, setSelectedTrendingCategory] = useState('All');
  const [trendingProducts, setTrendingProducts] = useState([]);

  const searchInputRef = useRef(null);
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Search functionality with intelligent redirection and color-specific filtering
  useEffect(() => {
    const performSearch = async () => {
      if (searchTerm.trim() === '') {
        setSearchResults([]);
        return;
      }

      // Check for category-specific terms and redirect
      const categoryRedirects = {
        'oversized': '/category/oversized-tees',
        'oversized tees': '/category/oversized-tees',
        'oversized t-shirt': '/category/oversized-t-shirt',
        'oversized shirt': '/category/oversized-shirt',
        't-shirts': '/category/t-shirts',
        'shirts': '/category/shirts',
        'hoodies': '/category/hoodies',
        'jackets': '/category/jackets',
        'baggy jeans': '/category/baggy-jeans',
        'fit jeans': '/category/fit-jeans',
        'capri': '/category/capri',
        'sweatshirt': '/category/sweatshirt',
        'shacket': '/category/shacket',
        'deals': '/category/deals'
      };

      const searchLower = searchTerm.toLowerCase().trim();
      if (categoryRedirects[searchLower]) {
        navigate(categoryRedirects[searchLower]);
        return;
      }

      try {
        // Use data service to search products
        const searchResponse = await dataService.searchProducts(searchTerm, {}, 1, 50);
        setSearchResults(searchResponse.products);
      } catch (error) {
        console.error('Search failed:', error);
        setSearchResults([]);
      }
    };

    performSearch();
  }, [searchTerm, navigate]);

  // Separate effect for updating recent searches to avoid infinite loop
  useEffect(() => {
    if (searchTerm.trim()) {
      setRecentSearches(prev => {
        const updated = [searchTerm, ...prev.filter(s => s !== searchTerm)].slice(0, 5);
        return updated;
      });
    }
  }, [searchTerm]);

  // Effect for filtering trending products based on selected category
  useEffect(() => {
    const loadTrendingProducts = async () => {
      try {
        if (selectedTrendingCategory === 'All') {
          // Get featured/bestseller products for mixed display
          const featuredProducts = await dataService.getFeaturedProducts(24);
          setTrendingProducts(featuredProducts);
        } else {
          // Get products by specific category
          const categoryProducts = await dataService.getProductsByCategory(selectedTrendingCategory, {}, 1, 24);
          setTrendingProducts(categoryProducts.products);
        }
      } catch (error) {
        console.error('Failed to load trending products:', error);
        setTrendingProducts([]);
      }
    };

    loadTrendingProducts();
  }, [selectedTrendingCategory]);

  const handleSearch = (term) => {
    setSearchTerm(term);
    setShowQuickSearch(false);
    searchInputRef.current?.focus();
  };

  const handleTrendingCategorySelect = (category) => {
    setSelectedTrendingCategory(category);
  };

  const clearSearch = () => {
    setSearchTerm('');
    setShowQuickSearch(false);
    searchInputRef.current?.focus();
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-teal-500/3 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Enhanced Search Header */}
      <div className="sticky top-0 z-40 bg-black/95 backdrop-blur-xl border-b border-[#2a2a2a] shadow-2xl">
        <div className="container mx-auto px-4 md:px-6 py-4 md:py-6">
          <div className="flex items-center gap-3 md:gap-6">
            <button
              onClick={() => navigate(-1)}
              className="group p-2 md:p-3 text-gray-400 hover:text-white transition-all duration-300 rounded-xl hover:bg-[#2a2a2a] hover:scale-110"
            >
              <ArrowLeft size={20} className="md:w-6 md:h-6 transition-transform group-hover:-translate-x-1" />
            </button>

            <div className="relative flex-1">
              <div className={`relative flex items-center bg-[#0a0a0a] rounded-xl md:rounded-2xl border transition-all duration-500 overflow-hidden group ${
                isSearchFocused
                  ? 'border-[#404040] shadow-[0_0_30px_rgba(64,64,64,0.15)] bg-[#1a1a1a]'
                  : 'border-[#2a2a2a] hover:border-[#404040]'
              }`}>
                <Search size={18} className={`absolute left-3 md:left-5 transition-colors duration-300 md:w-6 md:h-6 ${
                  isSearchFocused ? 'text-blue-400' : 'text-gray-400'
                }`} />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search for premium products..."
                  className="w-full py-3 md:py-5 pl-10 md:pl-14 pr-12 md:pr-16 bg-transparent text-white border-none outline-none text-sm md:text-lg placeholder:text-gray-500 font-medium"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onFocus={() => {
                    setIsSearchFocused(true);
                    if (!searchTerm) setShowQuickSearch(true);
                  }}
                  onBlur={() => {
                    setIsSearchFocused(false);
                    setTimeout(() => setShowQuickSearch(false), 200);
                  }}
                />
                {searchTerm && (
                  <button
                    className="absolute right-3 p-1.5 md:p-2 text-gray-400 hover:text-white transition-all duration-300 rounded-lg hover:bg-[#2a2a2a]"
                    onClick={clearSearch}
                  >
                    <X size={16} className="md:w-5 md:h-5" />
                  </button>
                )}
              </div>

              {/* Search suggestions overlay */}
              {showQuickSearch && !searchTerm && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-[#0a0a0a] backdrop-blur-xl rounded-xl border border-[#2a2a2a] shadow-2xl z-30 overflow-hidden">
                  <div className="p-4">
                    <h4 className="text-gray-400 text-sm font-medium mb-3 flex items-center gap-2">
                      <Clock size={14} />
                      Quick Search
                    </h4>
                    <div className="space-y-2">
                      {topSearches.slice(0, 2).map((term, index) => (
                        <button
                          key={index}
                          onClick={() => handleSearch(term)}
                          className="w-full text-left px-3 py-2 text-gray-300 hover:text-white hover:bg-[#2a2a2a] rounded-lg transition-all duration-200 flex items-center gap-3"
                        >
                          <TrendingUp size={14} className="text-gray-500" />
                          {term}
                        </button>
                      ))}
                      <div className="border-t border-[#2a2a2a] pt-2 mt-2">
                        <h5 className="text-gray-500 text-xs font-medium mb-2">Color-specific searches:</h5>
                        {colorSearchSuggestions.slice(0, 2).map((term, index) => (
                          <button
                            key={`color-${index}`}
                            onClick={() => handleSearch(term)}
                            className="w-full text-left px-3 py-2 text-gray-300 hover:text-white hover:bg-[#2a2a2a] rounded-lg transition-all duration-200 flex items-center gap-3"
                          >
                            <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                            {term}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>



      <div className="container mx-auto px-4 md:px-6 py-4 md:py-6 relative z-10">
        <AnimatedContent
          searchTerm={searchTerm}
          searchResults={searchResults}
          handleSearch={handleSearch}
          selectedTrendingCategory={selectedTrendingCategory}
          handleTrendingCategorySelect={handleTrendingCategorySelect}
          trendingProducts={trendingProducts}
        />
      </div>
    </div>
  );
}

function AnimatedContent({
  searchTerm,
  searchResults,
  handleSearch,
  selectedTrendingCategory,
  handleTrendingCategorySelect,
  trendingProducts
}) {
  return (
    <div className="space-y-8 md:space-y-12">
      {searchTerm ? (
        <SearchResults
          results={searchResults}
          searchTerm={searchTerm}
        />
      ) : (
        <DefaultContent
          handleSearch={handleSearch}
          selectedTrendingCategory={selectedTrendingCategory}
          handleTrendingCategorySelect={handleTrendingCategorySelect}
          trendingProducts={trendingProducts}
        />
      )}
    </div>
  );
}

function SearchResults({ results, searchTerm }) {
  // Detect if search includes color
  const colorKeywords = [
    'black', 'white', 'gray', 'grey', 'blue', 'red', 'green', 'yellow',
    'orange', 'purple', 'pink', 'brown', 'beige', 'navy', 'maroon',
    'olive', 'teal', 'cyan', 'magenta', 'lime', 'indigo', 'violet',
    'cream', 'khaki', 'tan', 'charcoal', 'silver', 'gold'
  ];

  const searchWords = searchTerm.toLowerCase().split(' ');
  const detectedColor = searchWords.find(word => colorKeywords.includes(word));

  if (results.length === 0) {
    return (
      <div className="text-center py-12 md:py-20">
        <div className="relative inline-block">
          <div className="w-24 h-24 md:w-32 md:h-32 bg-[#1a1a1a] rounded-full flex items-center justify-center mb-4 md:mb-6 mx-auto relative overflow-hidden">
            <Search size={32} className="md:w-12 md:h-12 text-gray-600" />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] animate-[shimmer_2s_infinite]"></div>
          </div>
        </div>
        <h3 className="text-xl md:text-2xl font-bold text-white mb-2">
          No products found
        </h3>
        <p className="text-gray-400 text-sm md:text-lg mb-6 md:mb-8 px-4">
          We couldn't find any products matching "{searchTerm}"
          {detectedColor && (
            <span className="block mt-2">
              Try searching for <span className="text-blue-400 capitalize">"{detectedColor}"</span> products in different categories
            </span>
          )}
        </p>
        <div className="flex justify-center">
          <button className="px-6 md:px-8 py-2.5 md:py-3 bg-gradient-to-r from-[#214FC3] to-[#54AEE1] text-white rounded-xl font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105 text-sm md:text-base">
            Browse All Products
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6 md:mb-8">
        <div>
          <h2 className="text-lg md:text-2xl font-bold text-white flex items-center gap-2 md:gap-3">
            <span className="text-blue-400">{results.length}</span>
            {results.length === 1 ? 'result' : 'results'} found
            <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-blue-500 rounded-full animate-pulse"></div>
          </h2>
          {detectedColor && (
            <p className="text-gray-400 text-sm mt-1">
              Showing <span className="text-blue-400 capitalize font-medium">{detectedColor}</span> products
            </p>
          )}
        </div>
      </div>

      <div className="grid gap-2 sm:gap-4 md:gap-5 grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4">
        {results.map((product, index) => (
          <ProductCard
            key={product.id}
            product={product}
            index={index}
            searchColor={detectedColor}
          />
        ))}
      </div>
    </div>
  );
}

function DefaultContent({
  handleSearch,
  selectedTrendingCategory,
  handleTrendingCategorySelect,
  trendingProducts
}) {
  const navigate = useNavigate();

  const handleCategoryClick = (categoryLink) => {
    navigate(categoryLink);
  };

  return (
    <div className="space-y-8 md:space-y-10">
      {/* Top Searches Section */}
      <div>
        <h3 className="text-center text-lg md:text-xl font-bold text-white mb-4 md:mb-6">
          TOP SEARCHES
        </h3>
        <div className="flex justify-center gap-2 md:gap-3 mb-8 overflow-x-auto scrollbar-hide">
          {topSearches.map((term, index) => (
            <button
              key={index}
              onClick={() => handleSearch(term)}
              className="px-4 py-2 border border-[#2a2a2a] text-gray-300 hover:text-white hover:border-[#404040] hover:bg-[#2a2a2a] rounded-lg transition-all duration-200 text-base md:text-md whitespace-nowrap"
            >
              {term}
            </button>
          ))}
        </div>
      </div>

      {/* Trending Section */}
      <div>
        <h3 className="text-center text-lg md:text-xl font-bold text-white mb-4 md:mb-6">
          TRENDING
        </h3>
        <div className="flex justify-center gap-2 md:gap-3 mb-8 overflow-x-auto scrollbar-hide px-2">
          {['All', 'Oversized Tees', 'T-Shirts', 'Hoodies', 'Shirts', 'Baggy Jeans'].map((category, index) => (
            <button
              key={index}
              onClick={() => handleTrendingCategorySelect(category)}
              className={`px-4 py-2 rounded-lg transition-all duration-200 text-base md:text-md font-medium whitespace-nowrap flex-shrink-0 ${
                selectedTrendingCategory === category
                  ? 'bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white'
                  : 'border border-[#2a2a2a] text-gray-300 hover:text-white hover:border-[#404040] hover:bg-[#2a2a2a]'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Trending Products Grid */}
        <div className="grid gap-2 sm:gap-4 md:gap-5 grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4">
          {trendingProducts.length > 0 ? (
            trendingProducts.map((product, index) => {
              console.log('Rendering product:', product);
              return <ProductCard key={product.id} product={product} index={index} />;
            })
          ) : (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-400">No trending products found for {selectedTrendingCategory}</p>
            </div>
          )}
        </div>
      </div>

    </div>
  );
}