<?php
/**
 * Notification Processor Script
 * 
 * Processes queued SMS and Email notifications asynchronously
 * Run this script via cron job every minute for optimal performance
 */

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");
            $_ENV[$key] = $value;
            putenv("$key=$value");
        }
    }
}

require_once __DIR__ . '/../config/database.php';

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

class NotificationProcessor
{
    private string $smsApiKey;
    private string $smsApiUrl;
    private string $smtpHost;
    private string $smtpUsername;
    private string $smtpPassword;
    private int $smtpPort;

    public function __construct()
    {
        // Load configuration from environment
        $this->smsApiKey = $_ENV['SMS_API_KEY'] ?? '';
        $this->smsApiUrl = $_ENV['SMS_API_URL'] ?? 'https://www.fast2sms.com/dev/bulkV2';
        $this->smtpHost = $_ENV['SMTP_HOST'] ?? 'smtp.gmail.com';
        $this->smtpUsername = $_ENV['SMTP_USERNAME'] ?? '';
        $this->smtpPassword = $_ENV['SMTP_PASSWORD'] ?? '';
        $this->smtpPort = (int)($_ENV['SMTP_PORT'] ?? 587);
    }

    /**
     * Process pending notifications
     */
    public function processPendingNotifications(): void
    {
        echo "🚀 Starting notification processing...\n";

        // Get pending notifications (limit to 50 per run to avoid memory issues)
        $sql = "SELECT * FROM notification_queue 
                WHERE status = 'pending' 
                AND attempts < max_attempts 
                AND (scheduled_at IS NULL OR scheduled_at <= NOW())
                ORDER BY created_at ASC 
                LIMIT 50";

        $statement = Database::execute($sql);
        $notifications = $statement->fetchAll();

        if (empty($notifications)) {
            echo "✅ No pending notifications found.\n";
            return;
        }

        echo "📧 Processing " . count($notifications) . " notifications...\n";

        foreach ($notifications as $notification) {
            $this->processNotification($notification);
        }

        echo "✅ Notification processing complete.\n";
    }

    /**
     * Process single notification
     */
    private function processNotification(array $notification): void
    {
        try {
            // Mark as processing
            $this->updateNotificationStatus($notification['id'], 'processing');

            $success = false;

            if ($notification['type'] === 'sms') {
                $success = $this->sendSMS($notification);
            } elseif ($notification['type'] === 'email') {
                $success = $this->sendEmail($notification);
            }

            if ($success) {
                $this->updateNotificationStatus($notification['id'], 'sent', null, date('Y-m-d H:i:s'));
                echo "✅ {$notification['type']} sent to {$notification['recipient']}\n";
            } else {
                $this->handleFailedNotification($notification);
            }

        } catch (\Exception $e) {
            $this->handleFailedNotification($notification, $e->getMessage());
        }
    }

    /**
     * Send SMS notification
     */
    private function sendSMS(array $notification): bool
    {
        if (empty($this->smsApiKey)) {
            // Development mode - just log the SMS
            error_log("SMS (DEV MODE): {$notification['recipient']} - {$notification['content']}");
            return true;
        }

        try {
            $data = [
                'authorization' => $this->smsApiKey,
                'message' => $notification['content'],
                'numbers' => $notification['recipient'],
                'route' => 'dlt'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->smsApiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $result = json_decode($response, true);
                return isset($result['return']) && $result['return'] === true;
            }

            return false;

        } catch (\Exception $e) {
            error_log("SMS sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send Email notification
     */
    private function sendEmail(array $notification): bool
    {
        if (empty($this->smtpUsername) || empty($this->smtpPassword)) {
            // Development mode - just log the email
            error_log("EMAIL (DEV MODE): {$notification['recipient']} - " . substr($notification['content'], 0, 100) . "...");
            return true;
        }

        try {
            $emailData = json_decode($notification['content'], true);
            
            if (!$emailData) {
                throw new \Exception('Invalid email data');
            }

            // Use PHPMailer or similar library for production
            // For now, using simple mail() function
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                'From: Wolffoxx Store <<EMAIL>>',
                'Reply-To: <EMAIL>',
                'X-Mailer: PHP/' . phpversion()
            ];

            $success = mail(
                $notification['recipient'],
                $emailData['subject'],
                $emailData['html'],
                implode("\r\n", $headers)
            );

            return $success;

        } catch (\Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle failed notification
     */
    private function handleFailedNotification(array $notification, string $errorMessage = null): void
    {
        $attempts = $notification['attempts'] + 1;
        
        if ($attempts >= $notification['max_attempts']) {
            // Max attempts reached - mark as failed
            $this->updateNotificationStatus($notification['id'], 'failed', $errorMessage);
            echo "❌ {$notification['type']} failed permanently for {$notification['recipient']}\n";
        } else {
            // Retry later with exponential backoff
            $retryDelay = pow(2, $attempts) * 60; // 2, 4, 8 minutes
            $scheduledAt = date('Y-m-d H:i:s', time() + $retryDelay);
            
            $this->updateNotificationStatus($notification['id'], 'pending', $errorMessage, null, $attempts, $scheduledAt);
            echo "⏳ {$notification['type']} retry scheduled for {$notification['recipient']} (attempt {$attempts})\n";
        }
    }

    /**
     * Update notification status
     */
    private function updateNotificationStatus(int $id, string $status, string $errorMessage = null, string $sentAt = null, int $attempts = null, string $scheduledAt = null): void
    {
        $updateFields = ['status = ?', 'updated_at = NOW()'];
        $params = [$status];

        if ($errorMessage !== null) {
            $updateFields[] = 'error_message = ?';
            $params[] = $errorMessage;
        }

        if ($sentAt !== null) {
            $updateFields[] = 'sent_at = ?';
            $params[] = $sentAt;
        }

        if ($attempts !== null) {
            $updateFields[] = 'attempts = ?';
            $params[] = $attempts;
        }

        if ($scheduledAt !== null) {
            $updateFields[] = 'scheduled_at = ?';
            $params[] = $scheduledAt;
        }

        $params[] = $id;

        $sql = "UPDATE notification_queue SET " . implode(', ', $updateFields) . " WHERE id = ?";
        Database::execute($sql, $params);
    }

    /**
     * Clean up old notifications
     */
    public function cleanupOldNotifications(): void
    {
        // Delete notifications older than 30 days
        $sql = "DELETE FROM notification_queue WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $statement = Database::execute($sql);
        $deletedCount = $statement->rowCount();

        if ($deletedCount > 0) {
            echo "🧹 Cleaned up {$deletedCount} old notifications\n";
        }
    }

    /**
     * Show notification statistics
     */
    public function showStats(): void
    {
        $sql = "SELECT 
                    status,
                    type,
                    COUNT(*) as count
                FROM notification_queue 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY status, type
                ORDER BY status, type";

        $statement = Database::execute($sql);
        $stats = $statement->fetchAll();

        echo "\n📊 NOTIFICATION STATS (Last 24 hours):\n";
        echo "=====================================\n";

        if (empty($stats)) {
            echo "No notifications in the last 24 hours.\n";
            return;
        }

        foreach ($stats as $stat) {
            echo sprintf("%-10s %-5s: %d\n", $stat['status'], $stat['type'], $stat['count']);
        }
        echo "\n";
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $processor = new NotificationProcessor();

    if ($argc > 1) {
        $command = $argv[1];
        
        switch ($command) {
            case 'process':
                $processor->processPendingNotifications();
                break;
                
            case 'cleanup':
                $processor->cleanupOldNotifications();
                break;
                
            case 'stats':
                $processor->showStats();
                break;
                
            default:
                echo "Usage: php process_notifications.php [process|cleanup|stats]\n";
        }
    } else {
        // Default action - process notifications
        $processor->processPendingNotifications();
    }
} else {
    echo "This script must be run from command line.\n";
}
