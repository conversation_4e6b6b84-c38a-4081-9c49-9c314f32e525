// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

// API Error Class
class APIError extends Error {
  constructor(message, status, data = null) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.data = data;
  }
}

// HTTP Client with error handling
const httpClient = {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Add auth token if provided
    if (options.token) {
      config.headers.Authorization = `Bearer ${options.token}`;
    }

    try {
      const response = await fetch(url, config);

      // Parse response
      let data;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      // Handle HTTP errors
      if (!response.ok) {
        const errorMessage = data?.message || data?.error || `HTTP ${response.status}: ${response.statusText}`;
        throw new APIError(errorMessage, response.status, data);
      }

      // Return data from successful response
      return data?.data || data;

    } catch (error) {
      // Network or parsing errors
      if (error instanceof APIError) {
        throw error;
      }

      // Network error
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new APIError('Network error. Please check your connection.', 0);
      }

      // Other errors
      throw new APIError(error.message || 'An unexpected error occurred', 0);
    }
  },

  get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options });
  },

  post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
  },

  put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
  },

  delete(endpoint, options = {}) {
    return this.request(endpoint, { method: 'DELETE', ...options });
  },
};

// Phone number formatting utility
const formatPhoneNumber = (phone) => {
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');

  // Add country code if missing (assuming India +91)
  if (cleaned.length === 10) {
    return `91${cleaned}`;
  }

  return cleaned;
};

// Validation utilities
const validatePhone = (phone) => {
  // Guard against empty/null/undefined phone
  if (!phone || phone.trim() === '') {
    return false;
  }

  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length >= 10 && cleaned.length <= 15;
};

const validateOTP = (otp) => {
  return /^\d{4,8}$/.test(otp);
};

// Auth API Service
export const authAPI = {
  /**
   * Send OTP to phone number
   */
  async sendOTP(phone, purpose = 'login') {
    // Guard against empty phone
    if (!phone || phone.trim() === '') {
      throw new APIError('Phone number is required');
    }

    // Validate phone number
    if (!validatePhone(phone)) {
      throw new APIError('Please enter a valid phone number');
    }

    // Send raw phone number - let backend handle formatting
    const cleanPhone = phone.replace(/\D/g, '');

    try {
      const response = await httpClient.post('/auth/otp/send', {
        phone: cleanPhone,
        purpose
      });

      return {
        phone: response.phone,
        message: response.message,
        expires_in: response.expires_in,
        can_resend_after: response.can_resend_after
      };
    } catch (error) {
      console.error('Send OTP error:', error);
      throw error;
    }
  },

  /**
   * Verify OTP and login/register
   */
  async verifyOTP(phone, otp, userDetails = {}) {
    // Debug logging
    console.log('verifyOTP called with:', { phone, otp: otp.substring(0, 2) + '****', userDetails });

    // Validate inputs
    if (!validatePhone(phone)) {
      console.error('Phone validation failed for:', phone);
      throw new APIError('Please enter a valid phone number');
    }

    if (!validateOTP(otp)) {
      console.error('OTP validation failed for:', otp);
      throw new APIError('Please enter a valid OTP');
    }

    // Send raw phone number - let backend handle formatting
    const cleanPhone = phone.replace(/\D/g, '');
    console.log('Sending to backend - cleanPhone:', cleanPhone);

    try {
      const response = await httpClient.post('/auth/otp/verify', {
        phone: cleanPhone,
        otp,
        user_details: userDetails
      });

      return {
        user: response.user,
        tokens: response.tokens,
        is_new_user: response.is_new_user,
        message: response.message
      };
    } catch (error) {
      console.error('Verify OTP error:', error);
      throw error;
    }
  },

  /**
   * Resend OTP
   */
  async resendOTP(phone, purpose = 'login') {
    // Guard against empty phone
    if (!phone || phone.trim() === '') {
      throw new APIError('Phone number is required');
    }

    // Validate phone number
    if (!validatePhone(phone)) {
      throw new APIError('Please enter a valid phone number');
    }

    // Send raw phone number - let backend handle formatting
    const cleanPhone = phone.replace(/\D/g, '');

    try {
      const response = await httpClient.post('/auth/otp/resend', {
        phone: cleanPhone,
        purpose
      });

      return {
        phone: response.phone,
        message: 'OTP resent successfully',
        expires_in: response.expires_in,
        can_resend_after: response.can_resend_after
      };
    } catch (error) {
      console.error('Resend OTP error:', error);
      throw error;
    }
  },

  /**
   * Update user profile
   */
  async updateProfile(profileData, token) {
    if (!token) {
      throw new APIError('Authentication required');
    }

    try {
      const response = await httpClient.put('/auth/otp/profile', profileData, {
        token
      });

      return {
        profile: response.profile,
        message: response.message
      };
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  },

  /**
   * Get user profile
   */
  async getProfile(token) {
    if (!token) {
      throw new APIError('Authentication required');
    }

    try {
      const response = await httpClient.get('/users/profile', {
        token
      });

      return response;
    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  },

  /**
   * Logout user
   */
  async logout(token) {
    if (!token) {
      return; // No token, nothing to logout
    }

    try {
      await httpClient.post('/auth/logout', {}, {
        token
      });
    } catch (error) {
      console.error('Logout error:', error);
      // Don't throw error for logout - we'll clear local data anyway
    }
  },

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken) {
    if (!refreshToken) {
      throw new APIError('Refresh token required');
    }

    try {
      const response = await httpClient.post('/auth/refresh', {
        refresh_token: refreshToken
      });

      return {
        access_token: response.access_token,
        refresh_token: response.refresh_token,
        expires_in: response.expires_in
      };
    } catch (error) {
      console.error('Refresh token error:', error);
      throw error;
    }
  },

  /**
   * Upload profile image
   */
  async uploadProfileImage(imageFile, token) {
    if (!token) {
      throw new APIError('Authentication required');
    }

    if (!imageFile) {
      throw new APIError('Image file required');
    }

    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await httpClient.request('/users/profile/image', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          // Don't set Content-Type for FormData - browser will set it with boundary
        },
        body: formData
      });

      return response;
    } catch (error) {
      console.error('Upload profile image error:', error);
      throw error;
    }
  },

  /**
   * Delete profile image
   */
  async deleteProfileImage(token) {
    if (!token) {
      throw new APIError('Authentication required');
    }

    try {
      const response = await httpClient.delete('/users/profile/image', {
        token
      });

      return response;
    } catch (error) {
      console.error('Delete profile image error:', error);
      throw error;
    }
  }
};

// Export utilities for use in components
export { validatePhone, validateOTP, formatPhoneNumber, APIError };
