<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - <?= htmlspecialchars($order['order_number']) ?></title>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }
        .order-number {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 6px;
            margin-top: 15px;
            display: inline-block;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
        }
        .order-summary {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            color: #1f2937;
        }
        .item-variant {
            color: #6b7280;
            font-size: 14px;
        }
        .item-price {
            font-weight: 600;
            color: #1f2937;
        }
        .total-section {
            border-top: 2px solid #e5e7eb;
            padding-top: 15px;
            margin-top: 15px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
        .total-final {
            font-weight: 700;
            font-size: 18px;
            color: #1f2937;
        }
        .shipping-info {
            background-color: #f0f9ff;
            border-left: 4px solid #3b82f6;
            padding: 20px;
            margin: 20px 0;
        }
        .footer {
            background-color: #f8fafc;
            padding: 30px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .footer a {
            color: #3b82f6;
            text-decoration: none;
        }
        .track-button {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Order Confirmed!</h1>
            <div class="order-number">Order #<?= htmlspecialchars($order['order_number']) ?></div>
        </div>
        
        <div class="content">
            <p>Hi <?= htmlspecialchars($user['first_name'] ?? 'Customer') ?>,</p>
            
            <p>Thank you for your order! We've received your order and it's being processed. You'll receive another email when your order ships.</p>
            
            <div class="order-summary">
                <h3 style="margin-top: 0;">Order Summary</h3>
                
                <?php foreach ($orderItems as $item): ?>
                <div class="order-item">
                    <div class="item-details">
                        <div class="item-name"><?= htmlspecialchars($item['product_name']) ?></div>
                        <?php if (!empty($item['selected_color']) || !empty($item['selected_size'])): ?>
                        <div class="item-variant">
                            <?php if (!empty($item['selected_color'])): ?>
                                Color: <?= htmlspecialchars($item['selected_color']) ?>
                            <?php endif; ?>
                            <?php if (!empty($item['selected_size'])): ?>
                                <?= !empty($item['selected_color']) ? ' | ' : '' ?>Size: <?= htmlspecialchars($item['selected_size']) ?>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        <div class="item-variant">Qty: <?= (int)$item['quantity'] ?></div>
                    </div>
                    <div class="item-price">₹<?= number_format($item['price'] * $item['quantity'], 2) ?></div>
                </div>
                <?php endforeach; ?>
                
                <div class="total-section">
                    <div class="total-row">
                        <span>Subtotal:</span>
                        <span>₹<?= number_format($order['subtotal'], 2) ?></span>
                    </div>
                    <?php if ($order['tax_amount'] > 0): ?>
                    <div class="total-row">
                        <span>Tax:</span>
                        <span>₹<?= number_format($order['tax_amount'], 2) ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if ($order['shipping_amount'] > 0): ?>
                    <div class="total-row">
                        <span>Shipping:</span>
                        <span>₹<?= number_format($order['shipping_amount'], 2) ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if ($order['discount_amount'] > 0): ?>
                    <div class="total-row">
                        <span>Discount:</span>
                        <span>-₹<?= number_format($order['discount_amount'], 2) ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="total-row total-final">
                        <span>Total:</span>
                        <span>₹<?= number_format($order['total_amount'], 2) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="shipping-info">
                <h4 style="margin-top: 0;">Shipping Information</h4>
                <?php 
                $shippingAddress = is_string($order['shipping_address']) ? 
                    json_decode($order['shipping_address'], true) : 
                    $order['shipping_address'];
                ?>
                <p style="margin: 5px 0;">
                    <?= htmlspecialchars($shippingAddress['name'] ?? $user['first_name'] . ' ' . $user['last_name']) ?><br>
                    <?= htmlspecialchars($shippingAddress['address_line_1'] ?? '') ?><br>
                    <?php if (!empty($shippingAddress['address_line_2'])): ?>
                        <?= htmlspecialchars($shippingAddress['address_line_2']) ?><br>
                    <?php endif; ?>
                    <?= htmlspecialchars($shippingAddress['city'] ?? '') ?>, <?= htmlspecialchars($shippingAddress['state'] ?? '') ?> <?= htmlspecialchars($shippingAddress['postal_code'] ?? '') ?><br>
                    <?= htmlspecialchars($shippingAddress['country'] ?? 'India') ?>
                </p>
                <p><strong>Phone:</strong> <?= htmlspecialchars($shippingAddress['phone'] ?? $user['phone']) ?></p>
            </div>
            
            <div style="text-align: center;">
                <a href="<?= $_ENV['FRONTEND_URL'] ?? 'http://localhost:3000' ?>/orders/<?= $order['id'] ?>" class="track-button">
                    Track Your Order
                </a>
            </div>
            
            <p>If you have any questions about your order, please contact our customer support team.</p>
        </div>
        
        <div class="footer">
            <p>&copy; <?= date('Y') ?> Wolffoxx. All rights reserved.</p>
            <p>
                <a href="<?= $_ENV['FRONTEND_URL'] ?? 'http://localhost:3000' ?>">Visit our website</a> | 
                <a href="<?= $_ENV['FRONTEND_URL'] ?? 'http://localhost:3000' ?>/contact">Contact Support</a>
            </p>
            <p style="margin-top: 15px; font-size: 12px;">
                This email was sent to <?= htmlspecialchars($user['email'] ?? $order['customer_email']) ?>. 
                Order placed on <?= date('F j, Y \a\t g:i A', strtotime($order['created_at'])) ?>.
            </p>
        </div>
    </div>
</body>
</html>
