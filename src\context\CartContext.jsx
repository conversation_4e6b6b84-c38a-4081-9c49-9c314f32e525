import { createContext, useContext, useState, useRef, useEffect } from 'react';
import { dataService } from '../services/dataService';
import { useAuth } from './AuthContext';

// Define types as comments since JSX doesn't support TypeScript

// type CartItem = {
//   id: number;
//   name: string;
//   price: number;
//   color: string;
//   size: string;
//   quantity: number;
//   image: string;
// }

// type CartContextType = {
//   items: CartItem[];
//   addToCart: (item: Omit<CartItem, 'quantity'>, quantity?: number) => void;
//   removeFromCart: (id: number) => void;
//   updateQuantity: (id: number, quantity: number) => void;
//   clearCart: () => void;
//   totalItems: number;
//   subtotal: number;
// }

const CartContext = createContext(undefined);

export { CartContext };

export function CartProvider({ children }) {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);
  const { user, isAuthenticated } = useAuth();

  // Clear data immediately when user becomes null (logout)
  useEffect(() => {
    if (!user?.id) {
      setItems([]);
      setCurrentUserId(null);
    }
  }, [user?.id]);

  // Load cart from backend when user is authenticated
  useEffect(() => {
    const loadCart = async () => {
      // If user changed, clear previous user's data
      if (currentUserId && user?.id && currentUserId !== user.id) {
        setItems([]);
      }

      // Update current user ID
      setCurrentUserId(user?.id || null);

      if (!isAuthenticated || !user?.id) {
        // If not authenticated, clear cart data
        setItems([]);
        return;
      }

      try {
        setLoading(true);
        const cart = await dataService.getCart(user.id);

        // Transform backend cart items to frontend format
        const transformedItems = (cart.items || []).map(item => ({
          id: item.product_id || item.id,
          name: item.product_name || item.name,
          price: parseFloat(item.unit_price || item.price || 0),
          salePrice: item.sale_price ? parseFloat(item.sale_price) : null,
          color: item.selected_color || 'Default',
          size: item.selected_size || 'M',
          colorHex: item.selected_color_hex || '#000000',
          quantity: parseInt(item.quantity || 1),
          image: item.product_image || item.image,
          sku: item.product_sku || item.sku,
          outfitName: item.outfit_name || null,
          cartItemId: item.id, // Backend cart item ID for updates/deletes
          category: item.category || 'general'
        }));

        setItems(transformedItems);
      } catch (error) {
        console.error('Failed to load cart:', error);
        // Fallback to localStorage
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
          setItems(JSON.parse(savedCart));
        }
      } finally {
        setLoading(false);
      }
    };

    loadCart();
  }, [isAuthenticated, user?.id]);

  // Note: Removed localStorage backup to prevent data leakage between users

  const addToCart = async (product, quantity = 1) => {
    const itemKey = `${product.id}-${product.color}-${product.size}`;

    console.log('🛒 addToCart called with:', {
      productName: product.name,
      quantity,
      color: product.color,
      size: product.size,
      outfitName: product.outfitName,
      itemKey,
      operationId: product.operationId,
      timestamp: new Date().toISOString()
    });

    if (isAuthenticated && user?.id) {
      try {
        // Prepare cart item for backend - Enhanced for rich product data
        const selectedColor = product.color || (product.colors && product.colors[0]?.name) || 'Default';
        const selectedColorHex = product.colorHex ||
          (product.colors && product.colors.find(c => c.name === selectedColor)?.value) || '#000000';
        const selectedSize = product.size || (product.sizes && product.sizes[0]) || 'M';
        const productImage = product.image ||
          (product.colors && product.colors.find(c => c.name === selectedColor)?.images?.[0]) ||
          (product.images && product.images[0]) || null;

        const cartItem = {
          product_id: product.id,
          selected_color: selectedColor,
          selected_size: selectedSize,
          selected_color_hex: selectedColorHex,
          quantity: quantity,
          unit_price: product.salePrice || product.price,
          sale_price: product.salePrice || null,
          product_name: product.name,
          product_image: productImage,
          product_sku: product.sku || null,
          outfit_id: product.outfitId || null,
          outfit_name: product.outfitName || null
        };

        console.log('🔑 Calling dataService.addToCart with:', {
          userId: user.id,
          cartItem,
          timestamp: new Date().toISOString()
        });

        await dataService.addToCart(user.id, cartItem);

        console.log('✅ dataService.addToCart completed successfully');

        // Update local state
        setItems((prevItems) => {
          const existingItemIndex = prevItems.findIndex(
            (item) =>
              item.id === product.id &&
              item.color === product.color &&
              item.size === product.size
          );

          let newItems;
          if (existingItemIndex > -1) {
            newItems = [...prevItems];
            newItems[existingItemIndex].quantity += quantity;
          } else {
            newItems = [...prevItems, {
              ...product,
              quantity,
              outfitName: product.outfitName || null
            }];
          }
          return newItems;
        });
      } catch (error) {
        console.error('Failed to add to cart:', error);
        // Fallback to localStorage behavior
        setItems((prevItems) => {
          const existingItemIndex = prevItems.findIndex(
            (item) =>
              item.id === product.id &&
              item.color === product.color &&
              item.size === product.size
          );

          let newItems;
          if (existingItemIndex > -1) {
            newItems = [...prevItems];
            newItems[existingItemIndex].quantity += quantity;
          } else {
            newItems = [...prevItems, {
              ...product,
              quantity,
              outfitName: product.outfitName || null
            }];
          }
          return newItems;
        });
      }
    } else {
      // Not authenticated, use localStorage
      setItems((prevItems) => {
        const existingItemIndex = prevItems.findIndex(
          (item) =>
            item.id === product.id &&
            item.color === product.color &&
            item.size === product.size
        );

        let newItems;
        if (existingItemIndex > -1) {
          newItems = [...prevItems];
          newItems[existingItemIndex].quantity += quantity;
        } else {
          newItems = [...prevItems, {
            ...product,
            quantity,
            outfitName: product.outfitName || null
          }];
        }
        return newItems;
      });
    }
  };

  const removeFromCart = async (id, color, size) => {
    if (isAuthenticated && user?.id) {
      try {
        // Find the item to get its backend ID
        const item = items.find(item =>
          item.id === id && item.color === color && item.size === size
        );

        if (item && item.cartItemId) {
          await dataService.removeFromCart(user.id, item.cartItemId);
        }

        setItems((prevItems) =>
          prevItems.filter(
            (item) =>
              !(item.id === id && item.color === color && item.size === size)
          )
        );
      } catch (error) {
        console.error('Failed to remove from cart:', error);
        // Fallback to localStorage behavior
        setItems((prevItems) =>
          prevItems.filter(
            (item) =>
              !(item.id === id && item.color === color && item.size === size)
          )
        );
      }
    } else {
      // Not authenticated, use localStorage
      setItems((prevItems) =>
        prevItems.filter(
          (item) =>
            !(item.id === id && item.color === color && item.size === size)
        )
      );
    }
  };

  const updateQuantity = async (id, quantity, color, size) => {
    if (isAuthenticated && user?.id) {
      try {
        // Find the item to get its backend ID
        const item = items.find(item =>
          item.id === id && item.color === color && item.size === size
        );

        if (item && item.cartItemId) {
          await dataService.updateCartItem(user.id, item.cartItemId, Math.max(1, quantity));
        }

        setItems((prevItems) =>
          prevItems.map((item) =>
            item.id === id && item.color === color && item.size === size
              ? { ...item, quantity: Math.max(1, quantity) }
              : item
          )
        );
      } catch (error) {
        console.error('Failed to update cart quantity:', error);
        // Fallback to localStorage behavior
        setItems((prevItems) =>
          prevItems.map((item) =>
            item.id === id && item.color === color && item.size === size
              ? { ...item, quantity: Math.max(1, quantity) }
              : item
          )
        );
      }
    } else {
      // Not authenticated, use localStorage
      setItems((prevItems) =>
        prevItems.map((item) =>
          item.id === id && item.color === color && item.size === size
            ? { ...item, quantity: Math.max(1, quantity) }
            : item
        )
      );
    }
  };

  const clearCart = async () => {
    if (isAuthenticated && user?.id) {
      try {
        await dataService.clearCart(user.id);
        setItems([]);
      } catch (error) {
        console.error('Failed to clear cart:', error);
        // Fallback to localStorage behavior
        setItems([]);
      }
    } else {
      // Not authenticated, use localStorage
      setItems([]);
    }
    // Always clear localStorage backup
    localStorage.removeItem('cart');
  };

  const totalItems = items.reduce((total, item) => total + item.quantity, 0);

  const subtotal = items.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );

  // Group items by outfit
  const getItemsByOutfit = () => {
    const outfits = {};
    const individualItems = [];

    items.forEach(item => {
      if (item.outfitName) {
        if (!outfits[item.outfitName]) {
          outfits[item.outfitName] = [];
        }
        outfits[item.outfitName].push(item);
      } else {
        individualItems.push(item);
      }
    });

    return { outfits, individualItems };
  };

  return (
    <CartContext.Provider
      value={{
        items,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        totalItems,
        subtotal,
        getItemsByOutfit,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};