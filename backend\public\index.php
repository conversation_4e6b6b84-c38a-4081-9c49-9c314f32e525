<?php

/**
 * Wolffoxx Ecommerce Backend
 * Entry point for all API requests
 */

// Set error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Asia/Kolkata');

// Define paths
define('ROOT_PATH', dirname(__DIR__));
define('SRC_PATH', ROOT_PATH . '/src');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');

// Create storage directories if they don't exist
$storageDirs = [
    STORAGE_PATH,
    STORAGE_PATH . '/logs',
    STORAGE_PATH . '/uploads',
    STORAGE_PATH . '/cache'
];

foreach ($storageDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Load environment variables
$envFile = ROOT_PATH . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");
            $_ENV[$key] = $value;
            putenv("$key=$value");
        }
    }
}

// CORS Headers - Allow all origins for development
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Origin, Accept');
header('Access-Control-Max-Age: 86400'); // Cache preflight for 24 hours

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Set content type
header('Content-Type: application/json; charset=utf-8');

// Load Composer autoloader first
require_once ROOT_PATH . '/vendor/autoload.php';

// Autoloader for our custom classes
spl_autoload_register(function ($class) {
    // Convert namespace to file path
    $prefix = 'Wolffoxx\\';
    $baseDir = SRC_PATH . '/';

    // Check if class uses our namespace
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    // Get relative class name
    $relativeClass = substr($class, $len);

    // Replace namespace separators with directory separators
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';

    // Load the file if it exists
    if (file_exists($file)) {
        require $file;
    }
});

// Load configuration
require_once CONFIG_PATH . '/database.php';
require_once CONFIG_PATH . '/jwt.php';

// Import required classes
use Wolffoxx\Utils\Router;
use Wolffoxx\Utils\Response;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Config\Database;

// Initialize database
Database::init();

// Simple error logging function
function logError($message, $context = []) {
    $logPath = ROOT_PATH . '/storage/logs';
    if (!is_dir($logPath)) {
        mkdir($logPath, 0755, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
    $logEntry = "[{$timestamp}] ERROR: {$message}{$contextStr}" . PHP_EOL;
    file_put_contents($logPath . '/app.log', $logEntry, FILE_APPEND | LOCK_EX);
}

try {
    // Initialize router
    $router = new Router();

    // Health check endpoint
    $router->get('/health', function() {
        Response::success([
            'status' => 'OK',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0.0',
            'environment' => $_ENV['APP_ENV'] ?? 'development'
        ]);
    });

    // Database test endpoint
    $router->get('/test-db', function() {
        try {
            $connection = Database::getConnection();
            $statement = $connection->query('SELECT COUNT(*) as count FROM products');
            $result = $statement->fetch();

            Response::success([
                'database' => 'connected',
                'products_count' => $result['count'],
                'config' => Database::getConfig()
            ]);
        } catch (\Exception $e) {
            Response::error('Database connection failed: ' . $e->getMessage(), 500);
        }
    });

    // Simple products test endpoint
    $router->get('/test-products', function() {
        try {
            $connection = Database::getConnection();
            $statement = $connection->query('SELECT id, name, price FROM products LIMIT 5');
            $products = $statement->fetchAll();

            Response::success([
                'message' => 'Products retrieved successfully',
                'count' => count($products),
                'products' => $products
            ]);
        } catch (\Exception $e) {
            Response::error('Products test failed: ' . $e->getMessage(), 500);
        }
    });

    // Table structure test
    $router->get('/test-table', function() {
        try {
            $connection = Database::getConnection();
            $statement = $connection->query('DESCRIBE products');
            $columns = $statement->fetchAll();

            Response::success([
                'message' => 'Products table structure',
                'columns' => $columns
            ]);
        } catch (\Exception $e) {
            Response::error('Table structure test failed: ' . $e->getMessage(), 500);
        }
    });

    // Test OTP endpoint
    $router->get('/test-otp', function() {
        Response::success([
            'message' => 'OTP endpoint test',
            'note' => 'Use POST /api/v1/auth/otp/send with JSON body'
        ]);
    });

    // Root route - API info
    $router->get('/', function() {
        Response::success([
            'message' => 'Wolffoxx Ecommerce API',
            'version' => '1.0.0',
            'status' => 'running',
            'timestamp' => date('c'),
            'environment' => $_ENV['APP_ENV'] ?? 'development',
            'endpoints' => [
                'products' => '/api/v1/products',
                'categories' => '/api/v1/categories',
                'auth' => '/api/v1/auth/otp/send',
                'cart' => '/api/v1/cart',
                'health' => '/health'
            ]
        ]);
    });

    // API v1 routes
    $router->group('/api/v1', function($router) {

        // Product routes (public)
        $router->get('/products', 'Wolffoxx\\Controllers\\ProductController@index');
        $router->get('/products/{id}', 'Wolffoxx\\Controllers\\ProductController@show');
        $router->get('/products/category/{category}', 'Wolffoxx\\Controllers\\ProductController@getByCategory');
        $router->get('/products/search', 'Wolffoxx\\Controllers\\ProductController@search');
        $router->get('/categories', 'Wolffoxx\\Controllers\\ProductController@getCategories');

        // Cart routes (protected)
        $router->get('/cart', 'Wolffoxx\\Controllers\\CartController@getCart');
        $router->post('/cart/items', 'Wolffoxx\\Controllers\\CartController@addItem');
        $router->put('/cart/items/{id}', 'Wolffoxx\\Controllers\\CartController@updateItem');
        $router->delete('/cart/items/{id}', 'Wolffoxx\\Controllers\\CartController@removeItem');
        $router->delete('/cart/clear', 'Wolffoxx\\Controllers\\CartController@clearCart');

        // Wishlist and Outfit routes moved to bottom section (lines 621+)

        // Send OTP endpoint
        $router->post('/auth/otp/send', function() {
            try {
                // Get JSON input
                $input = file_get_contents('php://input');
                $data = json_decode($input, true);

                if (!$data || !isset($data['phone'])) {
                    Response::error('Phone number required', 400);
                    return;
                }

                $phone = $data['phone'];

                // Debug log
                $logPath = dirname(__DIR__) . '/storage/logs';
                $debugEntry = "[" . date('Y-m-d H:i:s') . "] SEND OTP - Received phone: '{$phone}'\n";
                file_put_contents($logPath . '/app.log', $debugEntry, FILE_APPEND | LOCK_EX);

                // Format phone number (add 91 if needed)
                $cleanPhone = preg_replace('/[^0-9]/', '', $phone);
                $debugEntry2 = "[" . date('Y-m-d H:i:s') . "] SEND OTP - After regex clean: '{$cleanPhone}', length: " . strlen($cleanPhone) . "\n";
                file_put_contents($logPath . '/app.log', $debugEntry2, FILE_APPEND | LOCK_EX);

                if (strlen($cleanPhone) === 10) {
                    $cleanPhone = '91' . $cleanPhone;
                }

                $debugEntry3 = "[" . date('Y-m-d H:i:s') . "] SEND OTP - Final formatted phone: '{$cleanPhone}'\n";
                file_put_contents($logPath . '/app.log', $debugEntry3, FILE_APPEND | LOCK_EX);

                // Generate OTP
                $otp = sprintf('%06d', mt_rand(100000, 999999));

                // Send SMS based on provider
                $smsProvider = $_ENV['SMS_PROVIDER'] ?? 'log';
                $smsSuccess = false;
                $smsMessage = '';

                if ($smsProvider === 'msg91') {
                    // MSG91 SMS
                    $authKey = $_ENV['MSG91_AUTH_KEY'] ?? '';
                    $senderId = $_ENV['MSG91_SENDER_ID'] ?? 'WOLFFOXX';
                    $route = $_ENV['MSG91_ROUTE'] ?? '4';

                    if (!empty($authKey)) {
                        $message = "Your Wolffoxx login OTP is: {$otp}. Valid for 5 minutes. Do not share this code.";

                        $postData = [
                            'authkey' => $authKey,
                            'mobiles' => $cleanPhone,
                            'message' => $message,
                            'sender' => $senderId,
                            'route' => $route,
                            'country' => '91'
                        ];

                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, 'https://control.msg91.com/api/sendhttp.php');
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));

                        $response = curl_exec($ch);
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);

                        // Log SMS result
                        $logPath = dirname(__DIR__) . '/storage/logs';
                        if (!is_dir($logPath)) {
                            mkdir($logPath, 0755, true);
                        }
                        $logEntry = "[" . date('Y-m-d H:i:s') . "] MSG91 SMS to {$cleanPhone}: OTP {$otp}, Response: {$response}, HTTP Code: {$httpCode}\n";
                        file_put_contents($logPath . '/sms.log', $logEntry, FILE_APPEND | LOCK_EX);

                        // Debug log for response analysis
                        $debugEntry = "[" . date('Y-m-d H:i:s') . "] MSG91 DEBUG - Response length: " . strlen($response) . ", Is hex: " . (ctype_xdigit($response) ? 'YES' : 'NO') . ", HTTP: {$httpCode}\n";
                        file_put_contents($logPath . '/app.log', $debugEntry, FILE_APPEND | LOCK_EX);

                        // MSG91 success detection - be more lenient
                        if ($httpCode === 200 && !empty($response)) {
                            // Check if response looks like a success (message ID or success indicator)
                            if (ctype_xdigit($response) ||
                                strpos($response, 'success') !== false ||
                                strlen($response) > 10) {
                                $smsSuccess = true;
                                $smsMessage = 'OTP sent to your phone successfully via MSG91';
                            } else {
                                $smsMessage = 'SMS sent but delivery status unknown (check your phone)';
                            }
                        } else {
                            $smsMessage = 'SMS API call failed (check logs)';
                        }
                    }
                }

                if (!$smsSuccess) {
                    // Development mode - just log
                    $logPath = dirname(__DIR__) . '/storage/logs';
                    if (!is_dir($logPath)) {
                        mkdir($logPath, 0755, true);
                    }
                    $logEntry = "[" . date('Y-m-d H:i:s') . "] DEV MODE - OTP for {$cleanPhone}: {$otp} (SMS Provider: {$smsProvider})\n";
                    file_put_contents($logPath . '/app.log', $logEntry, FILE_APPEND | LOCK_EX);
                    $smsMessage = 'OTP generated (development mode - check logs)';
                } else {
                    // Log successful SMS
                    $logPath = dirname(__DIR__) . '/storage/logs';
                    $logEntry = "[" . date('Y-m-d H:i:s') . "] SMS SUCCESS - OTP sent to {$cleanPhone}: {$otp}\n";
                    file_put_contents($logPath . '/app.log', $logEntry, FILE_APPEND | LOCK_EX);
                }

                // Store OTP in database/session for verification
                // For now, we'll store in a simple file
                $otpData = [
                    'phone' => $cleanPhone,
                    'otp' => $otp,
                    'expires_at' => time() + 300, // 5 minutes
                    'created_at' => time()
                ];
                file_put_contents($logPath . '/otp_' . $cleanPhone . '.json', json_encode($otpData));

                Response::success([
                    'message' => $smsMessage,
                    'phone' => substr($cleanPhone, 0, 3) . '****' . substr($cleanPhone, -3),
                    'expires_in' => 300,
                    'can_resend_after' => 60
                ]);

            } catch (\Exception $e) {
                Response::error('Failed to send OTP: ' . $e->getMessage(), 500);
            }
        });

        // OTP Verification endpoint - Fixed implementation with proper error handling
        $router->post('/auth/otp/verify', function() {
            try {
                // Debug log to confirm this route is being used
                $logPath = dirname(__DIR__) . '/storage/logs';
                $debugEntry = "[" . date('Y-m-d H:i:s') . "] FIXED ROUTE USED - OTP Verify endpoint called\n";
                file_put_contents($logPath . '/app.log', $debugEntry, FILE_APPEND | LOCK_EX);

                // Get JSON input
                $input = file_get_contents('php://input');
                $data = json_decode($input, true);

                if (!$data || !isset($data['phone']) || !isset($data['otp'])) {
                    Response::error('Phone number and OTP required', 400);
                    return;
                }

                $phone = $data['phone'];
                $enteredOtp = $data['otp'];
                $userDetails = $data['user_details'] ?? [];

                // Format phone number EXACTLY the same as send OTP
                $cleanPhone = preg_replace('/[^0-9]/', '', $phone);
                if (strlen($cleanPhone) === 10) {
                    $cleanPhone = '91' . $cleanPhone;
                }

                // Check stored OTP
                $otpFile = $logPath . '/otp_' . $cleanPhone . '.json';

                if (!file_exists($otpFile)) {
                    Response::error('OTP not found or expired', 400);
                    return;
                }

                $otpData = json_decode(file_get_contents($otpFile), true);

                // Check if OTP is expired
                if (time() > $otpData['expires_at']) {
                    unlink($otpFile); // Delete expired OTP
                    Response::error('OTP has expired', 400);
                    return;
                }

                // Verify OTP
                if ($otpData['otp'] !== $enteredOtp) {
                    Response::error('Invalid OTP', 400);
                    return;
                }

                // OTP is valid - delete it
                unlink($otpFile);

                // Check if user exists in database, create if not
                $userModel = new \Wolffoxx\Models\User();
                $user = $userModel->findByPhone($cleanPhone);

                $isNewUser = false;
                if (!$user) {
                    // Create new user
                    $userData = [
                        'phone' => $cleanPhone,
                        'auth_method' => 'phone',
                        'is_active' => true,
                        'first_name' => $userDetails['first_name'] ?? '',
                        'last_name' => $userDetails['last_name'] ?? '',
                        'email' => $userDetails['email'] ?? ''
                    ];
                    $user = $userModel->create($userData);

                    if (!$user || !isset($user['id'])) {
                        Response::error('Failed to create user account', 500);
                        return;
                    }

                    $isNewUser = true;
                }

                // Update last login - only if user exists and has valid ID
                if ($user && isset($user['id']) && $user['id']) {
                    $userModel->updateLastLogin($user['id']);
                }

                // Generate JWT token (simple version)
                $tokenData = [
                    'user_id' => $user['id'],
                    'uuid' => $user['uuid'],
                    'phone' => $cleanPhone,
                    'login_time' => time()
                ];

                $token = base64_encode(json_encode($tokenData));

                // Log successful login
                $logEntry = "[" . date('Y-m-d H:i:s') . "] LOGIN SUCCESS - Phone: {$cleanPhone}, User ID: {$user['id']}, UUID: {$user['uuid']}\n";
                file_put_contents($logPath . '/app.log', $logEntry, FILE_APPEND | LOCK_EX);

                Response::success([
                    'message' => 'Login successful',
                    'tokens' => [
                        'accessToken' => $token,
                        'refreshToken' => $token // For now, using same token
                    ],
                    'user' => [
                        'id' => $user['id'],
                        'uuid' => $user['uuid'],
                        'phone' => substr($cleanPhone, 0, 3) . '****' . substr($cleanPhone, -3),
                        'first_name' => $user['first_name'] ?? '',
                        'last_name' => $user['last_name'] ?? '',
                        'email' => $user['email'] ?? ''
                    ],
                    'is_new_user' => $isNewUser
                ]);

            } catch (\Exception $e) {
                $logPath = dirname(__DIR__) . '/storage/logs';
                $errorEntry = "[" . date('Y-m-d H:i:s') . "] OTP VERIFY ERROR - " . $e->getMessage() . "\n";
                file_put_contents($logPath . '/app.log', $errorEntry, FILE_APPEND | LOCK_EX);

                Response::error('Failed to verify OTP: ' . $e->getMessage(), 500);
            }
        });

        // Profile endpoints for OTP users
        $router->get('/users/profile', function() {
            try {
                // Get user from JWT token
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
                if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                    Response::error('Authorization token required', 401);
                    return;
                }

                $token = substr($authHeader, 7);
                $tokenData = json_decode(base64_decode($token), true);

                if (!$tokenData || !isset($tokenData['user_id'])) {
                    Response::error('Invalid token', 401);
                    return;
                }

                $userModel = new \Wolffoxx\Models\User();
                $user = $userModel->findById($tokenData['user_id']);

                if (!$user) {
                    Response::error('User not found', 404);
                    return;
                }

                // Remove sensitive fields
                unset($user['password_hash'], $user['email_verification_token'], $user['password_reset_token']);

                Response::success($user);
            } catch (\Exception $e) {
                Response::error('Failed to get profile: ' . $e->getMessage(), 500);
            }
        });

        $router->put('/auth/otp/profile', function() {
            try {
                // Get user from JWT token
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
                if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                    Response::error('Authorization token required', 401);
                    return;
                }

                $token = substr($authHeader, 7);
                $tokenData = json_decode(base64_decode($token), true);

                if (!$tokenData || !isset($tokenData['user_id'])) {
                    Response::error('Invalid token', 401);
                    return;
                }

                $input = file_get_contents('php://input');
                $data = json_decode($input, true);

                $userModel = new \Wolffoxx\Models\User();
                $user = $userModel->findById($tokenData['user_id']);

                if (!$user) {
                    Response::error('User not found', 404);
                    return;
                }

                // Update profile data in database
                $updateData = [
                    'first_name' => $data['first_name'] ?? '',
                    'last_name' => $data['last_name'] ?? '',
                    'email' => $data['email'] ?? '',
                    'date_of_birth' => $data['date_of_birth'] ?? null,
                    'gender' => $data['gender'] ?? null
                ];

                // Remove empty values
                $updateData = array_filter($updateData, function($value) {
                    return $value !== '' && $value !== null;
                });

                $updatedUser = $userModel->updateProfile($user['id'], $updateData);

                if (!$updatedUser) {
                    Response::error('Failed to update profile', 500);
                    return;
                }

                // Remove sensitive fields
                unset($updatedUser['password_hash'], $updatedUser['email_verification_token'], $updatedUser['password_reset_token']);

                Response::success([
                    'message' => 'Profile updated successfully',
                    'profile' => $updatedUser
                ]);
            } catch (\Exception $e) {
                Response::error('Failed to update profile: ' . $e->getMessage(), 500);
            }
        });

        $router->post('/auth/otp/resend', 'Wolffoxx\\Controllers\\OTPAuthController@resendOTP');

        // OTP Admin routes
        $router->get('/auth/otp/stats', 'Wolffoxx\\Controllers\\OTPAuthController@getOTPStats', [AuthMiddleware::class . ':admin']);
        $router->post('/auth/otp/cleanup', 'Wolffoxx\\Controllers\\OTPAuthController@cleanupOTPs', [AuthMiddleware::class . ':admin']);

        // Email-based Authentication routes (legacy)
        $router->post('/auth/register', 'Wolffoxx\\Controllers\\AuthController@register');
        $router->post('/auth/login', 'Wolffoxx\\Controllers\\AuthController@login');
        $router->post('/auth/logout', 'Wolffoxx\\Controllers\\AuthController@logout');
        $router->post('/auth/refresh', 'Wolffoxx\\Controllers\\AuthController@refresh');
        $router->post('/auth/forgot-password', 'Wolffoxx\\Controllers\\AuthController@forgotPassword');
        $router->post('/auth/reset-password', 'Wolffoxx\\Controllers\\AuthController@resetPassword');
        $router->get('/auth/verify-email/{token}', 'Wolffoxx\\Controllers\\AuthController@verifyEmail');

        // User routes (legacy - using controllers)
        // $router->get('/users/profile', 'Wolffoxx\\Controllers\\UserController@getProfile', [AuthMiddleware::class]);
        $router->put('/users/profile', 'Wolffoxx\\Controllers\\UserController@updateProfile', [AuthMiddleware::class]);
        $router->post('/users/profile/image', 'Wolffoxx\\Controllers\\UserController@uploadProfileImage', [AuthMiddleware::class]);
        $router->delete('/users/profile/image', 'Wolffoxx\\Controllers\\UserController@deleteProfileImage', [AuthMiddleware::class]);
        $router->get('/users/{id}', 'Wolffoxx\\Controllers\\UserController@getUser', [AuthMiddleware::class]);

        // Product routes
        $router->get('/products', 'Wolffoxx\\Controllers\\ProductController@index');
        $router->get('/products/{id}', 'Wolffoxx\\Controllers\\ProductController@show');
        $router->get('/products/category/{category}', 'Wolffoxx\\Controllers\\ProductController@getByCategory');
        $router->get('/products/search/{query}', 'Wolffoxx\\Controllers\\ProductController@search');
        $router->post('/products', 'Wolffoxx\\Controllers\\ProductController@store', [AuthMiddleware::class . ':admin']);
        $router->put('/products/{id}', 'Wolffoxx\\Controllers\\ProductController@update', [AuthMiddleware::class . ':admin']);
        $router->delete('/products/{id}', 'Wolffoxx\\Controllers\\ProductController@delete', [AuthMiddleware::class . ':admin']);

        // Wishlist routes (updated to match frontend calls)
        $router->get('/wishlist', 'Wolffoxx\\Controllers\\WishlistController@index');
        $router->post('/wishlist/items', 'Wolffoxx\\Controllers\\WishlistController@addItem');
        $router->delete('/wishlist/items/{id}', 'Wolffoxx\\Controllers\\WishlistController@removeItem');
        $router->delete('/wishlist/clear', 'Wolffoxx\\Controllers\\WishlistController@clear');

        // Outfit routes (added missing routes)
        $router->get('/outfits', 'Wolffoxx\\Controllers\\OutfitController@index');
        $router->post('/outfits', 'Wolffoxx\\Controllers\\OutfitController@create');
        $router->get('/outfits/{id}', 'Wolffoxx\\Controllers\\OutfitController@show');
        $router->put('/outfits/{id}', 'Wolffoxx\\Controllers\\OutfitController@update');
        $router->delete('/outfits/{id}', 'Wolffoxx\\Controllers\\OutfitController@delete');
        $router->post('/outfits/{id}/items', 'Wolffoxx\\Controllers\\OutfitController@addItem');
        $router->delete('/outfits/{id}/items/{itemId}', 'Wolffoxx\\Controllers\\OutfitController@removeItem');

        // Order routes (protected)
        $router->post('/orders', 'Wolffoxx\\Controllers\\OrderController@create', [AuthMiddleware::class]);
        $router->get('/orders', 'Wolffoxx\\Controllers\\OrderController@getUserOrders', [AuthMiddleware::class]);
        $router->get('/orders/{id}', 'Wolffoxx\\Controllers\\OrderController@show', [AuthMiddleware::class]);
        $router->put('/orders/{id}/status', 'Wolffoxx\\Controllers\\OrderController@updateStatus', [AuthMiddleware::class]);
        $router->get('/orders/{id}/track', 'Wolffoxx\\Controllers\\OrderController@trackOrder', [AuthMiddleware::class]);

        // Payment routes (protected)
        $router->post('/payments/create-order', 'Wolffoxx\\Controllers\\PaymentController@createRazorpayOrder', [AuthMiddleware::class]);
        $router->post('/payments/verify', 'Wolffoxx\\Controllers\\PaymentController@verifyPayment', [AuthMiddleware::class]);
        $router->post('/payments/failure', 'Wolffoxx\\Controllers\\PaymentController@handlePaymentFailure', [AuthMiddleware::class]);
        $router->get('/payments/{payment_id}/status', 'Wolffoxx\\Controllers\\PaymentController@getPaymentStatus', [AuthMiddleware::class]);

        // Profile routes (protected)
        $router->get('/profile', 'Wolffoxx\\Controllers\\ProfileController@getProfile', [AuthMiddleware::class]);
        $router->put('/profile', 'Wolffoxx\\Controllers\\ProfileController@updateProfile', [AuthMiddleware::class]);
        $router->get('/profile/completion-status', 'Wolffoxx\\Controllers\\ProfileController@getCompletionStatus', [AuthMiddleware::class]);
        $router->post('/profile/complete', 'Wolffoxx\\Controllers\\ProfileController@completeProfile', [AuthMiddleware::class]);
        $router->get('/profile/validate-for-order', 'Wolffoxx\\Controllers\\ProfileController@validateForOrder', [AuthMiddleware::class]);

    });

    // Handle the request
    $method = $_SERVER['REQUEST_METHOD'];
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

    // Debug: Show registered routes
    if ($uri === '/debug-routes') {
        echo '<pre>';
        echo "Registered routes:\n";
        foreach ($router->getRoutes() as $route) {
            echo "{$route['method']} {$route['path']}\n";
        }
        echo "\nCurrent request: {$method} {$uri}\n";
        echo '</pre>';
        exit;
    }

    $router->dispatch($method, $uri);

} catch (\Exception $e) {
    // Log the error
    logError('Application error: ' . $e->getMessage(), [
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);

    // Return error response
    http_response_code(500);
    Response::error('Internal server error', 500);
}
