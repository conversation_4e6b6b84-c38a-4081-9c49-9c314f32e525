<?php

namespace Wolffoxx\Controllers;

use Wolffoxx\Models\User;
use Wolffoxx\Config\JWTConfig;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Logger;
use Wolffoxx\Services\EmailService;
use Wolffoxx\Utils\Validator;

/**
 * Authentication Controller
 * 
 * Handles user registration, login, logout, password reset,
 * and email verification functionality.
 */
class AuthController
{
    private User $userModel;
    private Logger $logger;
    private EmailService $emailService;

    public function __construct()
    {
        $this->userModel = new User();
        $this->logger = new Logger('auth');
        $this->emailService = new EmailService();
    }

    /**
     * User registration
     */
    public function register(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'email' => 'required|email|max:255',
                'password' => 'required|min:8|max:255',
                'first_name' => 'required|string|max:100',
                'last_name' => 'required|string|max:100',
                'phone' => 'nullable|string|max:20',
                'date_of_birth' => 'nullable|date',
                'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
                'newsletter_subscribed' => 'nullable|boolean',
                'marketing_emails' => 'nullable|boolean'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Check if email already exists
            if ($this->userModel->emailExists($data['email'])) {
                Response::error('Email already registered', 409);
                return;
            }

            // Create user
            $user = $this->userModel->createUser($data);

            if (!$user) {
                Response::error('Failed to create user account');
                return;
            }

            // Send verification email
            $this->sendVerificationEmail($user);

            // Generate tokens
            $tokens = JWTConfig::generateTokenPair([
                'user_id' => $user['id'],
                'email' => $user['email'],
                'role' => $user['is_admin'] ? 'admin' : 'user'
            ]);

            $this->logger->info('User registered successfully', [
                'user_id' => $user['id'],
                'email' => $user['email']
            ]);

            Response::created([
                'user' => $user,
                'tokens' => $tokens,
                'message' => 'Account created successfully. Please check your email to verify your account.'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Registration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            Response::error('Registration failed. Please try again.');
        }
    }

    /**
     * User login
     */
    public function login(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'email' => 'required|email',
                'password' => 'required|string',
                'remember_me' => 'nullable|boolean'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Check if user is locked
            if ($this->userModel->isUserLocked($data['email'])) {
                Response::error('Account temporarily locked due to multiple failed login attempts. Please try again later.', 423);
                return;
            }

            // Find user by email
            $user = $this->userModel->findByEmail($data['email']);

            if (!$user) {
                $this->userModel->incrementLoginAttempts($data['email']);
                Response::unauthorized('Invalid email or password');
                return;
            }

            // Verify password
            if (!$this->userModel->verifyPassword($data['password'], $user['password_hash'])) {
                $this->userModel->incrementLoginAttempts($data['email']);
                Response::unauthorized('Invalid email or password');
                return;
            }

            // Check if user is active
            if (!$user['is_active']) {
                Response::forbidden('Account is inactive. Please contact support.');
                return;
            }

            // Update last login
            $this->userModel->updateLastLogin($user['id']);

            // Generate tokens
            $tokens = JWTConfig::generateTokenPair([
                'user_id' => $user['id'],
                'email' => $user['email'],
                'role' => $user['is_admin'] ? 'admin' : 'user'
            ]);

            $this->logger->info('User logged in successfully', [
                'user_id' => $user['id'],
                'email' => $user['email']
            ]);

            Response::success([
                'user' => $user,
                'tokens' => $tokens,
                'message' => 'Login successful'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Login failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            Response::error('Login failed. Please try again.');
        }
    }

    /**
     * User logout
     */
    public function logout(array $params = []): void
    {
        try {
            $token = JWTConfig::extractTokenFromHeader();

            if ($token) {
                // Blacklist the token
                JWTConfig::blacklistToken($token);
                
                $this->logger->info('User logged out', [
                    'token_preview' => substr($token, 0, 20) . '...'
                ]);
            }

            Response::success(['message' => 'Logout successful']);

        } catch (\Exception $e) {
            $this->logger->error('Logout failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Logout failed');
        }
    }

    /**
     * Refresh access token
     */
    public function refresh(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'refresh_token' => 'required|string'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $refreshToken = $input['refresh_token'];

            // Refresh the token
            $newTokens = JWTConfig::refreshAccessToken($refreshToken);

            if (!$newTokens) {
                Response::unauthorized('Invalid or expired refresh token');
                return;
            }

            $this->logger->info('Token refreshed successfully');

            Response::success([
                'tokens' => $newTokens,
                'message' => 'Token refreshed successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Token refresh failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Token refresh failed');
        }
    }

    /**
     * Forgot password
     */
    public function forgotPassword(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'email' => 'required|email'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $email = $input['email'];

            // Find user by email
            $user = $this->userModel->findByEmail($email);

            if (!$user) {
                // Don't reveal if email exists or not
                Response::success(['message' => 'If the email exists, a password reset link has been sent.']);
                return;
            }

            // Generate password reset token
            $resetToken = $this->userModel->setPasswordResetToken($user['id']);

            // Send password reset email
            $this->sendPasswordResetEmail($user, $resetToken);

            $this->logger->info('Password reset requested', [
                'user_id' => $user['id'],
                'email' => $user['email']
            ]);

            Response::success(['message' => 'If the email exists, a password reset link has been sent.']);

        } catch (\Exception $e) {
            $this->logger->error('Forgot password failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to process password reset request');
        }
    }

    /**
     * Reset password
     */
    public function resetPassword(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'token' => 'required|string',
                'password' => 'required|min:8|max:255',
                'password_confirmation' => 'required|same:password'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $token = $input['token'];
            $password = $input['password'];

            // Find user by reset token
            $user = $this->userModel->findByPasswordResetToken($token);

            if (!$user) {
                Response::error('Invalid or expired reset token', 400);
                return;
            }

            // Update password
            $updated = $this->userModel->updatePassword($user['id'], $password);

            if (!$updated) {
                Response::error('Failed to update password');
                return;
            }

            $this->logger->info('Password reset successfully', [
                'user_id' => $user['id'],
                'email' => $user['email']
            ]);

            Response::success(['message' => 'Password reset successfully']);

        } catch (\Exception $e) {
            $this->logger->error('Password reset failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Password reset failed');
        }
    }

    /**
     * Verify email
     */
    public function verifyEmail(array $params = []): void
    {
        try {
            $token = $params['token'] ?? '';

            if (empty($token)) {
                Response::error('Verification token is required', 400);
                return;
            }

            // Verify email
            $verified = $this->userModel->verifyEmail($token);

            if (!$verified) {
                Response::error('Invalid or expired verification token', 400);
                return;
            }

            $this->logger->info('Email verified successfully', [
                'token_preview' => substr($token, 0, 8) . '...'
            ]);

            Response::success(['message' => 'Email verified successfully']);

        } catch (\Exception $e) {
            $this->logger->error('Email verification failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Email verification failed');
        }
    }

    /**
     * Get JSON input from request body
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Response::error('Invalid JSON input', 400);
            exit;
        }

        return $data ?? [];
    }

    /**
     * Send verification email
     */
    private function sendVerificationEmail(array $user): void
    {
        try {
            $verificationUrl = $_ENV['FRONTEND_URL'] . '/verify-email/' . $user['email_verification_token'];

            $this->emailService->send(
                $user['email'],
                'Verify Your Email Address',
                'emails/verify-email',
                [
                    'user' => $user,
                    'verification_url' => $verificationUrl
                ]
            );

        } catch (\Exception $e) {
            $this->logger->error('Failed to send verification email', [
                'user_id' => $user['id'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send password reset email
     */
    private function sendPasswordResetEmail(array $user, string $resetToken): void
    {
        try {
            $resetUrl = $_ENV['FRONTEND_URL'] . '/reset-password/' . $resetToken;

            $this->emailService->send(
                $user['email'],
                'Reset Your Password',
                'emails/reset-password',
                [
                    'user' => $user,
                    'reset_url' => $resetUrl
                ]
            );

        } catch (\Exception $e) {
            $this->logger->error('Failed to send password reset email', [
                'user_id' => $user['id'],
                'error' => $e->getMessage()
            ]);
        }
    }
}
