<?php

namespace Wolffoxx\Utils;

/**
 * Authentication Helper
 * Simple JWT token handling for OTP-based authentication
 */
class AuthHelper
{
    /**
     * Get authenticated user from JWT token
     */
    public static function getAuthenticatedUser(): ?array
    {
        try {
            // Get authorization header
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return null;
            }

            // Extract token
            $token = substr($authHeader, 7);
            
            // Decode token (simple base64 for now)
            $tokenData = json_decode(base64_decode($token), true);
            
            if (!$tokenData || !isset($tokenData['user_id'])) {
                return null;
            }

            // Validate token is not expired (optional)
            $loginTime = $tokenData['login_time'] ?? 0;
            $maxAge = 24 * 60 * 60; // 24 hours
            
            if (time() - $loginTime > $maxAge) {
                return null; // Token expired
            }

            // Return user data from token
            return [
                'id' => $tokenData['user_id'],
                'uuid' => $tokenData['uuid'] ?? '',
                'phone' => $tokenData['phone'] ?? '',
                'login_time' => $loginTime
            ];

        } catch (\Exception $e) {
            error_log('Auth token validation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get current user ID
     */
    public static function getCurrentUserId(): ?int
    {
        $user = self::getAuthenticatedUser();
        return $user ? (int)$user['id'] : null;
    }

    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated(): bool
    {
        return self::getAuthenticatedUser() !== null;
    }

    /**
     * Generate simple JWT token
     */
    public static function generateToken(array $userData): string
    {
        $tokenData = [
            'user_id' => $userData['id'],
            'uuid' => $userData['uuid'] ?? '',
            'phone' => $userData['phone'] ?? '',
            'login_time' => time()
        ];

        return base64_encode(json_encode($tokenData));
    }
}
