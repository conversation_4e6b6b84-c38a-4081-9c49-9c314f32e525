import { useState, useEffect, useMemo, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SlidersHorizontal,
  Filter,
  X,
  ChevronDown,
  Search,
  Star,
  Home,
  ChevronRight,
  Grid3X3,
  Grid2X2
} from 'lucide-react';
import { dataService } from '../services/dataService';
import ProductCard from '../components/ProductCard';


const categoryTitles = {
  'oversized-tees': 'Oversized Tees',
  't-shirts': 'T-Shirts',
  'hoodies': 'Hoodies',
  'shirts': 'Shirts',
  'capri': 'Capri',
  'baggy-jeans': 'Baggy Jeans',
  'fit-jeans': 'Fit Jeans',
  'jackets': 'Jackets',
  'sweatshirt': 'Sweatshirt',
  'shacket': 'Shacket',
  'oversized-t-shirt': 'Oversized T-Shirt',
  'oversized-shirt': 'Oversized Shirt',
  'full-sleeves-shirt': 'Full Sleeves Shirt',
  'regular-fit-t-shirt': 'Regular Fit T-Shirt',
  'bestsellers': 'Bestsellers',
  'deals': 'Deals'
};

const sizeOptions = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];
const colorOptions = [
  { name: 'Black', hex: '#000000' },
  { name: 'White', hex: '#FFFFFF' },
  { name: 'Navy Blue', hex: '#1e3a8a' },
  { name: 'Gray', hex: '#6b7280' },
  { name: 'Red', hex: '#dc2626' },
  { name: 'Green', hex: '#16a34a' },
  { name: 'Blue', hex: '#2563eb' },
  { name: 'Orange', hex: '#ea580c' },
  { name: 'Purple', hex: '#9333ea' },
  { name: 'Pink', hex: '#ec4899' },
  { name: 'Beige', hex: '#d4a574' },
  { name: 'Olive', hex: '#84cc16' },
  { name: 'Cream', hex: '#f5f5dc' },
  { name: 'Red Plaid', hex: '#dc2626' },
  { name: 'Light Wash', hex: '#6b9bd1' },
  { name: 'Dark Wash', hex: '#1e3a8a' }
];

const materialOptions = ['100% Cotton', '60% Cotton, 40% Polyester', '80% Cotton, 20% Polyester', '75% Cotton, 25% Polyester', '100% Cotton Flannel', '100% Cotton Denim'];
const fitOptions = ['Regular', 'Slim', 'Relaxed', 'Oversized', 'Athletic'];

export default function CategoryPage() {
  const { category } = useParams();
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter states
  const [sortBy, setSortBy] = useState('newest');
  const [priceRange, setPriceRange] = useState([0, 200]);
  const [selectedSizes, setSelectedSizes] = useState([]);
  const [selectedColors, setSelectedColors] = useState([]);
  const [selectedMaterials, setSelectedMaterials] = useState([]);
  const [selectedFits, setSelectedFits] = useState([]);
  const [minRating, setMinRating] = useState(0);
  const [inStock, setInStock] = useState(false);
  const [onSale, setOnSale] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(12);

  // Mobile view mode (single column vs two columns)
  const [mobileViewMode, setMobileViewMode] = useState('double'); // 'single' or 'double'

  // Filter panel states
  const [expandedSections, setExpandedSections] = useState({
    search: true,
    price: true,
    size: true,
    color: true,
    material: true,
    fit: true,
    rating: false,
    additional: true
  });

  // Scroll to top when component mounts or category changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [category]);

  useEffect(() => {
    const loadCategoryProducts = async () => {
      setIsLoading(true);
      try {
        let products;

        // Map URL categories to exact product categories
        const categoryMap = {
          'oversized-tees': 'Oversized Tees',
          't-shirts': 'T-Shirts',
          'hoodies': 'Hoodies',
          'shirts': 'Shirts',
          'capri': 'Capri',
          'baggy-jeans': 'Baggy Jeans',
          'fit-jeans': 'Fit Jeans',
          'jackets': 'Jackets',
          'sweatshirt': 'Sweatshirt',
          'shacket': 'Shacket',
          'oversized-t-shirt': 'Oversized T-Shirt',
          'oversized-shirt': 'Oversized Shirt',
          'full-sleeves-shirt': 'Full Sleeves Shirt',
          'regular-fit-t-shirt': 'Regular Fit T-Shirt'
        };

        // Use proper API calls with your real categories table
        if (category === 'bestsellers') {
          products = await dataService.getBestsellerProducts(50);
        } else if (category === 'deals') {
          const saleProducts = await dataService.getSaleProducts({}, 1, 50);
          products = saleProducts.products;
        } else {
          const targetCategory = categoryMap[category];
          if (targetCategory) {
            const categoryProducts = await dataService.getProductsByCategory(targetCategory, {}, 1, 50);
            products = categoryProducts.products;
          } else {
            products = [];
          }
        }

        setFilteredProducts(products);
      } catch (error) {
        console.error('Failed to load category products:', error);
        setFilteredProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadCategoryProducts();
  }, [category]);

  const processedProducts = useMemo(() => {
    let filtered = [...filteredProducts];

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(product =>
        product.name?.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query)
      );
    }

    // Price filter
    filtered = filtered.filter(product => {
      const price = product.salePrice || product.price || 0;
      return price >= priceRange[0] && price <= priceRange[1];
    });

    // Size filter
    if (selectedSizes.length > 0) {
      filtered = filtered.filter(product =>
        product.sizes?.some(size => selectedSizes.includes(size))
      );
    }

    // Color filter - Updated to work with new color structure
    if (selectedColors.length > 0) {
      filtered = filtered.filter(product =>
        product.colors?.some(color => selectedColors.includes(color.name))
      );
    }

    // Material filter
    if (selectedMaterials.length > 0) {
      filtered = filtered.filter(product =>
        selectedMaterials.includes(product.material)
      );
    }

    // Fit filter
    if (selectedFits.length > 0) {
      filtered = filtered.filter(product =>
        selectedFits.includes(product.fit)
      );
    }

    // Rating filter
    if (minRating > 0) {
      filtered = filtered.filter(product =>
        (product.rating || 0) >= minRating
      );
    }

    // Stock filter
    if (inStock) {
      filtered = filtered.filter(product => product.inStock !== false);
    }

    // Sale filter
    if (onSale) {
      filtered = filtered.filter(product => product.salePrice);
    }

    // Sort products
    switch (sortBy) {
      case 'price-low':
        return filtered.sort((a, b) => (a.salePrice || a.price || 0) - (b.salePrice || b.price || 0));
      case 'price-high':
        return filtered.sort((a, b) => (b.salePrice || b.price || 0) - (a.salePrice || a.price || 0));
      case 'rating':
        return filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      case 'name':
        return filtered.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
      case 'newest':
      default:
        return filtered.sort((a, b) => new Date(b.dateAdded || 0) - new Date(a.dateAdded || 0));
    }
  }, [filteredProducts, searchQuery, priceRange, selectedSizes, selectedColors, selectedMaterials, selectedFits, minRating, inStock, onSale, sortBy]);

  // Pagination logic
  const totalPages = Math.ceil(processedProducts.length / productsPerPage);
  const startIndex = (currentPage - 1) * productsPerPage;
  const endIndex = startIndex + productsPerPage;
  const currentProducts = processedProducts.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, priceRange, selectedSizes, selectedColors, selectedMaterials, selectedFits, minRating, inStock, onSale, sortBy]);

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const clearAllFilters = useCallback(() => {
    setSearchQuery('');
    setPriceRange([0, 200]);
    setSelectedSizes([]);
    setSelectedColors([]);
    setSelectedMaterials([]);
    setSelectedFits([]);
    setMinRating(0);
    setInStock(false);
    setOnSale(false);
  }, []);

  // Stable handlers to prevent re-renders and focus loss
  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleSearchClear = useCallback(() => {
    setSearchQuery('');
  }, []);

  const handleMinPriceChange = useCallback((e) => {
    const value = parseInt(e.target.value) || 0;
    setPriceRange(prev => [Math.min(value, prev[1]), prev[1]]);
  }, []);

  const handleMaxPriceChange = useCallback((e) => {
    const value = parseInt(e.target.value) || 200;
    setPriceRange(prev => [prev[0], Math.max(value, prev[0])]);
  }, []);

  const handleMinRangeChange = useCallback((e) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value)) {
      setPriceRange(prev => [Math.min(value, prev[1]), prev[1]]);
    }
  }, []);

  const handleMaxRangeChange = useCallback((e) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value)) {
      setPriceRange(prev => [prev[0], Math.max(value, prev[0])]);
    }
  }, []);

  const activeFiltersCount = useMemo(() => [
    searchQuery,
    selectedSizes.length > 0,
    selectedColors.length > 0,
    selectedMaterials.length > 0,
    selectedFits.length > 0,
    minRating > 0,
    inStock,
    onSale,
    priceRange[0] > 0 || priceRange[1] < 200
  ].filter(Boolean).length, [
    searchQuery,
    selectedSizes,
    selectedColors,
    selectedMaterials,
    selectedFits,
    minRating,
    inStock,
    onSale,
    priceRange
  ]);

  const FilterSection = ({ title, children, section }) => {
    const handleToggle = useCallback(() => {
      toggleSection(section);
    }, [section]);

    return (
      <div className="border-b border-slate-800/60 pb-5 mb-5 last:border-b-0">
        <button
          onClick={handleToggle}
          className="flex items-center justify-between w-full text-left text-white font-semibold mb-4 hover:text-cyan-400 transition-all duration-300 group"
        >
          <span className="text-sm uppercase tracking-wider font-bold">{title}</span>
          <div className={`p-1.5 rounded-full transition-all duration-300 ${expandedSections[section] ? 'bg-cyan-500/20 rotate-180' : 'group-hover:bg-slate-800/70'}`}>
            <ChevronDown size={14} className="transition-transform duration-300" />
          </div>
        </button>
        <AnimatePresence>
          {expandedSections[section] && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const FilterContent = () => {
    return (
      <div className="space-y-5">
        {/* Sort By */}
        <FilterSection title="Sort By" section="sort">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="w-full appearance-none bg-slate-800/80 border border-slate-700/50 text-white px-4 py-3 pr-10 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-500/50 hover:bg-slate-700/80 cursor-pointer transition-all duration-200 font-medium"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
              backgroundPosition: 'right 0.75rem center',
              backgroundRepeat: 'no-repeat',
              backgroundSize: '1.5em 1.5em'
            }}
          >
            <option value="newest">Newest First</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="rating">Highest Rated</option>
            <option value="name">Name A-Z</option>
          </select>
        </FilterSection>

        <FilterSection title="Search Products" section="search">
          <div className="relative">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
            <input
              type="text"
              placeholder="Search by name or description..."
              value={searchQuery}
              onChange={handleSearchChange}
              autoComplete="off"
              className="w-full pl-10 pr-4 py-3 bg-slate-800/60 border border-slate-700/50 text-white placeholder-slate-400 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-500/50 hover:bg-slate-700/60 transition-all duration-200 backdrop-blur-sm"
            />
            {searchQuery && (
              <button
                onClick={handleSearchClear}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
              >
                <X size={16} />
              </button>
            )}
          </div>
        </FilterSection>

        {/* Price Range */}
        <FilterSection title="Price Range" section="price">
          <div className="space-y-4">
            {/* Dual Range Slider */}
            <div className="relative px-2 range-container">
              <div className="relative h-2 bg-slate-800 rounded-lg">
                <div
                  className="absolute h-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg"
                  style={{
                    left: `${(priceRange[0] / 200) * 100}%`,
                    width: `${((priceRange[1] - priceRange[0]) / 200) * 100}%`
                  }}
                />
              </div>

              {/* Min Range Slider */}
              <input
                type="range"
                min="0"
                max="200"
                value={priceRange[0]}
                onChange={handleMinRangeChange}
                className="absolute top-0 left-0 w-full h-2 bg-transparent appearance-none cursor-pointer pointer-events-auto range-slider"
                style={{
                  background: 'transparent',
                  WebkitAppearance: 'none',
                  MozAppearance: 'none',
                  zIndex: 2
                }}
              />

              {/* Max Range Slider */}
              <input
                type="range"
                min="0"
                max="200"
                value={priceRange[1]}
                onChange={handleMaxRangeChange}
                className="absolute top-0 left-0 w-full h-2 bg-transparent appearance-none cursor-pointer pointer-events-auto range-slider"
                style={{
                  background: 'transparent',
                  WebkitAppearance: 'none',
                  MozAppearance: 'none',
                  zIndex: 2
                }}
              />
            </div>

            {/* Price Input Fields */}
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <label className="block text-xs text-slate-400 mb-1 font-medium">Min Price</label>
                <input
                  type="number"
                  min="0"
                  max="200"
                  value={priceRange[0]}
                  onChange={handleMinPriceChange}
                  autoComplete="off"
                  className="w-full px-3 py-2 bg-slate-800/60 border border-slate-700/50 text-white text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500/50 hover:bg-slate-700/60 transition-all duration-200"
                  placeholder="0"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs text-slate-400 mb-1 font-medium">Max Price</label>
                <input
                  type="number"
                  min="0"
                  max="200"
                  value={priceRange[1]}
                  onChange={handleMaxPriceChange}
                  autoComplete="off"
                  className="w-full px-3 py-2 bg-slate-800/60 border border-slate-700/50 text-white text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500/50 hover:bg-slate-700/60 transition-all duration-200"
                  placeholder="200"
                />
              </div>
            </div>
          </div>
        </FilterSection>

        {/* Colors */}
        <FilterSection title="Colors" section="color">
          <div className="grid grid-cols-5 gap-2.5">
            {colorOptions.map((color) => (
              <motion.button
                key={color.name}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  setSelectedColors(prev =>
                    prev.includes(color.name)
                      ? prev.filter(c => c !== color.name)
                      : [...prev, color.name]
                  );
                }}
                className={`relative w-10 h-10 rounded-full border-2 transition-all duration-300 shadow-lg ${
                  selectedColors.includes(color.name)
                    ? 'border-cyan-400 ring-2 ring-cyan-400/40 scale-110'
                    : 'border-slate-600 hover:border-slate-500'
                }`}
                style={{ backgroundColor: color.hex }}
                title={color.name}
              >
                {selectedColors.includes(color.name) && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full shadow-lg"></div>
                  </div>
                )}
              </motion.button>
            ))}
          </div>
        </FilterSection>

        {/* Sizes */}
        <FilterSection title="Sizes" section="size">
          <div className="grid grid-cols-4 gap-2">
            {sizeOptions.map((size) => (
              <motion.button
                key={size}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  setSelectedSizes(prev =>
                    prev.includes(size)
                      ? prev.filter(s => s !== size)
                      : [...prev, size]
                  );
                }}
                className={`px-3 py-2.5 rounded-lg text-sm font-bold transition-all duration-300 shadow-lg ${
                  selectedSizes.includes(size)
                    ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white ring-2 ring-cyan-400/40'
                    : 'bg-slate-800/60 text-slate-300 hover:bg-slate-700/60 hover:text-white border border-slate-700/50'
                }`}
              >
                {size}
              </motion.button>
            ))}
          </div>
        </FilterSection>

        {/* Materials */}
        <FilterSection title="Materials" section="material">
          <div className="space-y-3">
            {materialOptions.map((material) => (
              <label key={material} className="flex items-center space-x-3 cursor-pointer group">
                <input
                  type="checkbox"
                  checked={selectedMaterials.includes(material)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedMaterials([...selectedMaterials, material]);
                    } else {
                      setSelectedMaterials(selectedMaterials.filter(m => m !== material));
                    }
                  }}
                  className="w-4 h-4 text-cyan-500 bg-slate-800 border-slate-600 rounded focus:ring-cyan-500/50 focus:ring-2"
                />
                <span className="text-slate-300 group-hover:text-white transition-colors font-medium">{material}</span>
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Fit */}
        <FilterSection title="Fit" section="fit">
          <div className="space-y-3">
            {fitOptions.map((fit) => (
              <label key={fit} className="flex items-center space-x-3 cursor-pointer group">
                <input
                  type="checkbox"
                  checked={selectedFits.includes(fit)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedFits([...selectedFits, fit]);
                    } else {
                      setSelectedFits(selectedFits.filter(f => f !== fit));
                    }
                  }}
                  className="w-4 h-4 text-cyan-500 bg-slate-800 border-slate-600 rounded focus:ring-cyan-500/50 focus:ring-2"
                />
                <span className="text-slate-300 group-hover:text-white transition-colors font-medium">{fit}</span>
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Rating */}
        <FilterSection title="Minimum Rating" section="rating">
          <div className="space-y-3">
            {[5, 4, 3, 2, 1].map((rating) => (
              <label key={rating} className="flex items-center space-x-3 cursor-pointer group">
                <input
                  type="radio"
                  name="rating"
                  checked={minRating === rating}
                  onChange={() => setMinRating(rating)}
                  className="w-4 h-4 text-cyan-500 bg-slate-800 border-slate-600 focus:ring-cyan-500/50 focus:ring-2"
                />
                <div className="flex items-center space-x-2">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      size={16}
                      className={i < rating ? 'text-yellow-400 fill-current' : 'text-slate-600'}
                    />
                  ))}
                  <span className="text-slate-400 text-sm font-medium">& up</span>
                </div>
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Additional Filters */}
        <FilterSection title="Additional Filters" section="additional">
          <div className="space-y-4">
            <label className="flex items-center justify-between cursor-pointer group">
              <span className="text-slate-300 group-hover:text-white transition-colors font-medium">In Stock Only</span>
              <input
                type="checkbox"
                checked={inStock}
                onChange={(e) => setInStock(e.target.checked)}
                className="w-4 h-4 text-cyan-500 bg-slate-800 border-slate-600 rounded focus:ring-cyan-500/50 focus:ring-2"
              />
            </label>
            <label className="flex items-center justify-between cursor-pointer group">
              <span className="text-slate-300 group-hover:text-white transition-colors font-medium">On Sale</span>
              <input
                type="checkbox"
                checked={onSale}
                onChange={(e) => setOnSale(e.target.checked)}
                className="w-4 h-4 text-cyan-500 bg-slate-800 border-slate-600 rounded focus:ring-cyan-500/50 focus:ring-2"
              />
            </label>
          </div>
        </FilterSection>
      </div>
    );
  };

  return (
    <>
      {/* Custom CSS for range sliders and scrollbars */}
      <style>{`
        .range-slider {
          pointer-events: auto;
          z-index: 10;
        }

        .range-slider::-webkit-slider-thumb {
          appearance: none;
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: linear-gradient(135deg, #06b6d4, #3b82f6);
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
          position: relative;
          z-index: 20;
          pointer-events: auto;
        }

        .range-slider::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(0, 182, 212, 0.4);
        }

        .range-slider::-moz-range-thumb {
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: linear-gradient(135deg, #06b6d4, #3b82f6);
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
          position: relative;
          z-index: 20;
          pointer-events: auto;
        }

        .range-slider::-moz-range-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(0, 182, 212, 0.4);
        }

        .range-slider::-webkit-slider-track {
          background: transparent;
          height: 8px;
        }

        .range-slider::-moz-range-track {
          background: transparent;
          height: 8px;
        }

        .range-container {
          user-select: none;
        }

        /* Custom scrollbar styles */
        .filter-scroll {
          scrollbar-width: thin;
          scrollbar-color: #475569 #1e293b;
        }

        .filter-scroll::-webkit-scrollbar {
          width: 8px;
        }

        .filter-scroll::-webkit-scrollbar-track {
          background: #1e293b;
          border-radius: 4px;
          margin: 4px 0;
        }

        .filter-scroll::-webkit-scrollbar-thumb {
          background: #475569;
          border-radius: 4px;
          border: 1px solid #1e293b;
        }

        .filter-scroll::-webkit-scrollbar-thumb:hover {
          background: #64748b;
        }

        .filter-scroll::-webkit-scrollbar-corner {
          background: #1e293b;
        }
      `}</style>

      <div className="min-h-screen bg-black relative overflow-hidden w-full">

        <div className="relative z-10">
      {/* Mobile Filter Overlay */}
      <AnimatePresence>
        {showFilters && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40 lg:hidden"
              onClick={() => setShowFilters(false)}
            />
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', damping: 30, stiffness: 300 }}
              className="fixed top-0 left-0 h-full w-[85vw] max-w-sm bg-[#0a0a0a] backdrop-blur-xl border-r border-[#2a2a2a] z-50 lg:hidden overflow-y-auto shadow-2xl filter-scroll"
            >
              <div className="p-5">
                <div className="flex items-center justify-between mb-6 pb-4 border-b border-slate-800">
                  <h2 className="text-xl font-bold text-white flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-lg">
                      <SlidersHorizontal size={18} className="text-cyan-400" />
                    </div>
                    Filters
                    {activeFiltersCount > 0 && (
                      <span className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white text-xs px-2.5 py-1 rounded-full font-bold shadow-lg">
                        {activeFiltersCount}
                      </span>
                    )}
                  </h2>
                  <div className="flex items-center gap-2">
                    {activeFiltersCount > 0 && (
                      <button
                        onClick={clearAllFilters}
                        className="text-slate-400 hover:text-white text-sm font-medium transition-colors"
                      >
                        Clear All
                      </button>
                    )}
                    <button
                      onClick={() => setShowFilters(false)}
                      className="p-2.5 hover:bg-slate-800/70 rounded-xl transition-all duration-300 hover:scale-110"
                    >
                      <X size={18} className="text-gray-400" />
                    </button>
                  </div>
                </div>
                <FilterContent />
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      <div className="flex min-h-screen">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block w-80 bg-[#0a0a0a] backdrop-blur-xl border-r border-[#2a2a2a] sticky top-0 h-screen shadow-2xl">
          <div className="h-full flex flex-col">
            {/* Fixed Header */}
            <div className="flex items-center gap-4 px-6 py-4 border-b border-[#2a2a2a] flex-shrink-0 bg-[#1a1a1a]">
              <div className="p-3 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-xl">
                <SlidersHorizontal size={20} className="text-cyan-400" />
              </div>
              <h2 className="text-xl font-bold text-white">Filters</h2>
              {activeFiltersCount > 0 && (
                <span className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white text-xs px-3 py-1.5 rounded-full font-bold shadow-lg">
                  {activeFiltersCount}
                </span>
              )}
              <div className="ml-auto">
                {activeFiltersCount > 0 && (
                  <button
                    onClick={clearAllFilters}
                    className="text-slate-400 hover:text-white text-sm font-medium transition-colors"
                  >
                    Clear All
                  </button>
                )}
              </div>
            </div>
            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto filter-scroll" style={{ paddingRight: '4px' }}>
              <div className="p-6">
                <FilterContent />
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 pt-4 pb-6 w-full max-w-full">
          <div className="container mx-auto px-3 md:px-6 lg:px-8 max-w-7xl">
            {/* Navigation & Header Section */}
            {category && (
              <div className="mb-6">
                {/* Breadcrumb Navigation - Top Left */}
                <motion.nav
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-8"
                >
                  <div className="flex items-center space-x-2 text-sm">
                    <Link
                      to="/"
                      className="flex items-center text-slate-400 hover:text-cyan-400 transition-colors font-medium"
                    >
                      <Home size={16} className="mr-1" />
                      Home
                    </Link>
                    <ChevronRight size={14} className="text-slate-600" />
                    <Link
                      to="/collections"
                      className="text-slate-400 hover:text-cyan-400 transition-colors font-medium"
                    >
                      Collections
                    </Link>
                    <ChevronRight size={14} className="text-slate-600" />
                    <span className="text-white font-semibold">
                      {categoryTitles[category]}
                    </span>
                  </div>
                </motion.nav>



                {/* Clean Top Bar - View Options + Filters (Mobile Only) */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="lg:hidden flex items-center justify-between mb-4"
                >
                  {/* Left side - View Options */}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setMobileViewMode('single')}
                      className={`p-2.5 rounded-lg transition-all duration-200 ${
                        mobileViewMode === 'single'
                          ? 'bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white border border-orange-500/50 shadow-lg'
                          : 'text-slate-400 hover:text-white hover:bg-slate-800/60'
                      }`}
                      title="Single column view"
                    >
                      <Grid3X3 size={18} />
                    </button>
                    <button
                      onClick={() => setMobileViewMode('double')}
                      className={`p-2.5 rounded-lg transition-all duration-200 ${
                        mobileViewMode === 'double'
                          ? 'bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white border border-orange-500/50 shadow-lg'
                          : 'text-slate-400 hover:text-white hover:bg-slate-800/60'
                      }`}
                      title="Two columns view"
                    >
                      <Grid2X2 size={18} />
                    </button>
                  </div>

                  {/* Right side - Filters Icon */}
                  <button
                    onClick={() => setShowFilters(true)}
                    className="flex items-center gap-2 text-slate-300 hover:text-white transition-colors"
                  >
                    <Filter size={20} />
                    {activeFiltersCount > 0 && (
                      <span className="bg-cyan-500 text-white text-xs px-2 py-1 rounded-full font-bold min-w-[20px] h-5 flex items-center justify-center">
                        {activeFiltersCount}
                      </span>
                    )}
                  </button>
                </motion.div>
              </div>
            )}



            {/* Products Section */}
            {category && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mt-4"
              >
                {isLoading ? (
                  <div className={`grid gap-2 sm:gap-4 md:gap-5 ${
                    mobileViewMode === 'double'
                      ? 'grid-cols-2 gap-x-2 sm:grid-cols-2 sm:gap-x-4 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4'
                      : 'grid-cols-1 sm:grid-cols-2 sm:gap-x-4 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4'
                  }`}>
                    {[...Array(8)].map((_, i) => (
                      <div key={i} className="bg-[#0a0a0a] rounded-xl h-80 sm:h-88 md:h-96 lg:h-[26rem] animate-pulse border border-[#2a2a2a] shadow-xl" />
                    ))}
                  </div>
                ) : processedProducts.length === 0 ? (
                  <div className="text-center py-20">
                    <div className="bg-[#1a1a1a] rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6 shadow-xl">
                      <Search size={32} className="text-slate-400" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-3">No products found</h3>
                    <p className="text-slate-400 mb-8 text-lg">Try adjusting your filters or search terms</p>
                    <button
                      onClick={clearAllFilters}
                      className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white px-8 py-4 rounded-xl transition-all duration-300 hover:scale-105 shadow-lg font-semibold"
                    >
                      Clear all filters
                    </button>
                  </div>
                ) : (
                  <>
                    <div className={`grid gap-2 sm:gap-4 md:gap-5 ${
                      mobileViewMode === 'double'
                        ? 'grid-cols-2 gap-x-2 sm:grid-cols-2 sm:gap-x-4 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4'
                        : 'grid-cols-1 sm:grid-cols-2 sm:gap-x-4 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4'
                    }`}>
                      {currentProducts.map((product, index) => (
                        <motion.div
                          key={product.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                        >
                          <ProductCard product={product} index={index} />
                        </motion.div>
                      ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="flex items-center justify-center mt-6 gap-1 sm:gap-2"
                      >
                        <button
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                          className="px-3 py-2 bg-slate-800/60 hover:bg-slate-700/60 disabled:bg-slate-800/30 disabled:text-slate-500 text-white rounded-lg transition-all duration-200 disabled:cursor-not-allowed text-sm"
                        >
                          <span className="hidden sm:inline">Previous</span>
                          <span className="sm:hidden">Prev</span>
                        </button>

                        <div className="flex gap-1">
                          {[...Array(totalPages)].map((_, index) => {
                            const pageNumber = index + 1;
                            const isCurrentPage = pageNumber === currentPage;

                            // Show first page, last page, current page, and pages around current
                            if (
                              pageNumber === 1 ||
                              pageNumber === totalPages ||
                              (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                            ) {
                              return (
                                <button
                                  key={pageNumber}
                                  onClick={() => setCurrentPage(pageNumber)}
                                  className={`px-2 sm:px-3 py-2 rounded-lg transition-all duration-200 text-sm ${
                                    isCurrentPage
                                      ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white'
                                      : 'bg-slate-800/60 hover:bg-slate-700/60 text-slate-300 hover:text-white'
                                  }`}
                                >
                                  {pageNumber}
                                </button>
                              );
                            } else if (
                              pageNumber === currentPage - 2 ||
                              pageNumber === currentPage + 2
                            ) {
                              return (
                                <span key={pageNumber} className="px-2 py-2 text-slate-500">
                                  ...
                                </span>
                              );
                            }
                            return null;
                          })}
                        </div>

                        <button
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                          disabled={currentPage === totalPages}
                          className="px-3 py-2 bg-slate-800/60 hover:bg-slate-700/60 disabled:bg-slate-800/30 disabled:text-slate-500 text-white rounded-lg transition-all duration-200 disabled:cursor-not-allowed text-sm"
                        >
                          <span className="hidden sm:inline">Next</span>
                          <span className="sm:hidden">Next</span>
                        </button>
                      </motion.div>
                    )}
                  </>
                )}
              </motion.div>
            )}
          </div>
        </div>
      </div>
      </div>
      </div>
    </>
  );
}