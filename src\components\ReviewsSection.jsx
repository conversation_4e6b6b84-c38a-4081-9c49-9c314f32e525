import { useState } from 'react';
import { Check, ChevronDown, ChevronUp, Star, ThumbsUp } from 'lucide-react';
import { getProductReviews, getAverageRating, getRatingDistribution } from '../data/reviews';
import { motion, AnimatePresence } from 'framer-motion';

export default function ReviewsSection({ productId }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedRating, setSelectedRating] = useState(null);
  
  const reviews = getProductReviews(productId);
  const averageRating = getAverageRating(productId);
  const ratingDistribution = getRatingDistribution(productId);

  const filteredReviews = selectedRating
    ? reviews.filter((review) => review.rating === selectedRating)
    : reviews;

  // Calculate percentages for rating bars
  const totalReviews = reviews.length;
  const percentages = ratingDistribution.map((count) =>
    totalReviews > 0 ? (count / totalReviews) * 100 : 0
  );

  return (
    <section className="mt-16 pt-8 border-t border-gray-800">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-left"
      >
        <div className="flex items-baseline gap-4">
          <h2 className="text-2xl font-['Bebas_Neue',sans-serif] tracking-wider text-white">
            CUSTOMER REVIEWS
          </h2>
          <div className="flex items-center gap-1">
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  className={`${
                    averageRating >= star
                      ? 'fill-amber-400 text-amber-400'
                      : 'fill-gray-600 text-gray-600'
                  }`}
                />
              ))}
            </div>
            <span className="text-white font-medium ml-2">{averageRating}</span>
            <span className="text-gray-400 ml-1">({totalReviews})</span>
          </div>
        </div>
        <div>{isExpanded ? <ChevronUp className="text-gray-400" /> : <ChevronDown className="text-gray-400" />}</div>
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Rating Summary */}
              <div className="md:col-span-1">
                <div className="bg-gray-800/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-white mb-4">Rating Breakdown</h3>

                  {/* Rating Bars */}
                  <div className="space-y-3">
                    {[5, 4, 3, 2, 1].map((rating, index) => (
                      <button
                        key={rating}
                        onClick={() =>
                          setSelectedRating(selectedRating === rating ? null : rating)
                        }
                        className="w-full flex items-center gap-2 group hover:bg-gray-700/30 p-1 rounded transition-colors"
                      >
                        <div className="flex items-center gap-1 w-16">
                          <span className="text-sm text-white">{rating}</span>
                          <Star size={14} className="fill-amber-400 text-amber-400" />
                        </div>
                        <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                          <div
                            className={`h-full bg-amber-400 ${
                              selectedRating === rating ? 'bg-indigo-400' : ''
                            }`}
                            style={{ width: `${percentages[5 - rating]}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-400 w-8 text-right">
                          {ratingDistribution[5 - rating]}
                        </span>
                      </button>
                    ))}
                  </div>

                  {selectedRating && (
                    <button
                      onClick={() => setSelectedRating(null)}
                      className="mt-4 text-sm text-indigo-400 hover:text-indigo-300 transition-colors flex items-center gap-1"
                    >
                      Clear filter
                    </button>
                  )}
                </div>
              </div>

              {/* Reviews List */}
              <div className="md:col-span-2">
                <div className="space-y-6">
                  {filteredReviews.length > 0 ? (
                    filteredReviews.map((review) => (
                      <div key={review.id} className="border-b border-gray-800 pb-6">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center gap-3">
                            {review.userAvatar ? (
                              <img
                                src={review.userAvatar}
                                alt={review.username}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-full bg-indigo-900/50 flex items-center justify-center text-indigo-400 font-medium">
                                {review.username.charAt(0).toUpperCase()}
                              </div>
                            )}
                            <div>
                              <p className="font-medium text-white">{review.username}</p>
                              <div className="flex items-center gap-2 text-xs text-gray-400">
                                <span>{review.date}</span>
                                {review.verified && (
                                  <div className="flex items-center text-emerald-400">
                                    <Check size={12} className="mr-1" />
                                    <span>Verified Purchase</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                size={14}
                                className={`${
                                  review.rating >= star
                                    ? 'fill-amber-400 text-amber-400'
                                    : 'fill-gray-600 text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                        </div>

                        <h4 className="font-semibold text-white text-lg mb-2">{review.title}</h4>
                        <p className="text-gray-300 mb-4">{review.comment}</p>

                        <button className="flex items-center gap-1 text-sm text-gray-400 hover:text-white transition-colors">
                          <ThumbsUp size={14} />
                          <span>Helpful ({review.helpfulCount || 0})</span>
                        </button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-400">No reviews found with this rating.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}