<?php

namespace Wolffoxx\Models;

use Wolffoxx\Config\Database;

/**
 * Outfit Model
 * 
 * Handles outfit operations, outfit items,
 * and outfit-related database interactions.
 */
class Outfit extends BaseModel
{
    protected string $table = 'outfits';
    
    protected array $fillable = [
        'uuid',
        'user_id',
        'name',
        'description',
        'occasion',
        'season',
        'style',
        'color_scheme',
        'total_price',
        'total_sale_price',
        'is_public',
        'is_featured',
        'is_complete',
        'tags',
        'view_count',
        'like_count',
        'share_count'
    ];

    protected array $casts = [
        'user_id' => 'integer',
        'total_price' => 'float',
        'total_sale_price' => 'float',
        'is_public' => 'boolean',
        'is_featured' => 'boolean',
        'is_complete' => 'boolean',
        'view_count' => 'integer',
        'like_count' => 'integer',
        'share_count' => 'integer'
    ];

    /**
     * Get user's outfits with items
     */
    public function getUserOutfits(int $userId, array $filters = []): array
    {
        try {
            $sql = "SELECT o.*,
                           COUNT(oi.id) as item_count,
                           GROUP_CONCAT(DISTINCT c.name) as categories
                    FROM {$this->table} o
                    LEFT JOIN outfit_items oi ON o.id = oi.outfit_id
                    LEFT JOIN products p ON oi.product_id = p.id
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE o.user_id = ?";

            $params = [$userId];

            // Apply filters
            if (!empty($filters['occasion'])) {
                $sql .= " AND o.occasion = ?";
                $params[] = $filters['occasion'];
            }

            if (!empty($filters['season'])) {
                $sql .= " AND o.season = ?";
                $params[] = $filters['season'];
            }

            if (!empty($filters['is_public'])) {
                $sql .= " AND o.is_public = ?";
                $params[] = $filters['is_public'];
            }

            $sql .= " GROUP BY o.id";

            // Add sorting
            $sortBy = $filters['sort_by'] ?? 'created_at';
            $sortOrder = $filters['sort_order'] ?? 'DESC';
            
            $allowedSortFields = ['created_at', 'name', 'total_price', 'like_count', 'view_count'];
            if (in_array($sortBy, $allowedSortFields)) {
                $sql .= " ORDER BY o.{$sortBy} {$sortOrder}";
            }

            // Add pagination
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . (int)$filters['limit'];
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET " . (int)$filters['offset'];
                }
            }

            $statement = Database::execute($sql, $params);
            $results = $statement->fetchAll();

            // Get items for each outfit
            $outfits = array_map([$this, 'processOutfitResult'], $results);

            foreach ($outfits as &$outfit) {
                $outfit['items'] = $this->getOutfitItems($outfit['id']);
            }

            return $outfits;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Create new outfit
     */
    public function createOutfit(int $userId, array $data): ?array
    {
        try {
            $uuid = $this->generateUuid();

            $outfitData = [
                'uuid' => $uuid,
                'user_id' => $userId,
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'occasion' => $data['occasion'] ?? null,
                'season' => $data['season'] ?? 'all',
                'style' => $data['style'] ?? null,
                'color_scheme' => $data['color_scheme'] ?? null,
                'is_public' => $data['is_public'] ?? false,
                'tags' => $data['tags'] ?? null
            ];

            $outfit = $this->create($outfitData);

            if ($outfit && !empty($data['items'])) {
                $this->addItemsToOutfit($outfit['id'], $data['items']);
                $this->updateOutfitTotals($outfit['id']);

                // Reload outfit with updated totals
                $outfit = $this->findById($outfit['id']);
            }

            return $outfit;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Get outfit with items
     */
    public function getOutfitWithItems(int $outfitId): ?array
    {
        try {
            $outfit = $this->findById($outfitId);
            if (!$outfit) {
                return null;
            }

            $outfit['items'] = $this->getOutfitItems($outfitId);
            return $this->processOutfitResult($outfit);

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Get outfit items with product details
     */
    public function getOutfitItems(int $outfitId): array
    {
        try {
            $sql = "SELECT
                        oi.*,
                        p.name as product_name,
                        p.slug as product_slug,
                        c.name as category,
                        p.price as current_price,
                        p.sale_price as current_sale_price,
                        p.stock_quantity,
                        (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image
                    FROM outfit_items oi
                    JOIN products p ON oi.product_id = p.id
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE oi.outfit_id = ?
                    ORDER BY oi.sort_order ASC, oi.created_at ASC";

            $statement = Database::execute($sql, [$outfitId]);
            $items = $statement->fetchAll();

            // Enhance each item with additional data
            foreach ($items as &$item) {
                // Get all product images
                $imagesSql = "SELECT image_url, is_primary FROM product_images WHERE product_id = ? ORDER BY is_primary DESC, id ASC";
                $imagesStmt = Database::execute($imagesSql, [$item['product_id']]);
                $images = $imagesStmt->fetchAll();
                $item['images'] = array_column($images, 'image_url');

                // Get product colors with hex values (if table exists)
                try {
                    $colorsSql = "SELECT name, hex_value FROM product_colors WHERE product_id = ? ORDER BY id ASC";
                    $colorsStmt = Database::execute($colorsSql, [$item['product_id']]);
                    $colors = $colorsStmt->fetchAll();
                    $item['colors'] = $colors;
                } catch (\Exception $e) {
                    // Fallback if product_colors table doesn't exist
                    $item['colors'] = [];
                }

                // Get product sizes (if table exists)
                try {
                    $sizesSql = "SELECT size FROM product_sizes WHERE product_id = ? ORDER BY id ASC";
                    $sizesStmt = Database::execute($sizesSql, [$item['product_id']]);
                    $sizes = $sizesStmt->fetchAll();
                    $item['sizes'] = array_column($sizes, 'size');
                } catch (\Exception $e) {
                    // Fallback if product_sizes table doesn't exist
                    $item['sizes'] = ['S', 'M', 'L', 'XL']; // Default sizes
                }

                // Find matching color object for selected color
                if (!empty($item['selected_color'])) {
                    foreach ($colors as $color) {
                        if (strcasecmp($color['name'], $item['selected_color']) === 0) {
                            $item['matchingColor'] = [
                                'name' => $color['name'],
                                'hex' => $color['hex_value'],
                                'images' => $item['images'] // For now, use all images
                            ];
                            break;
                        }
                    }
                }
            }

            return $items;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Add items to outfit
     */
    public function addItemsToOutfit(int $outfitId, array $items): bool
    {
        try {
            foreach ($items as $item) {
                $this->addItemToOutfit($outfitId, $item);
            }

            $this->updateOutfitTotals($outfitId);
            return true;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Add single item to outfit
     */
    public function addItemToOutfit(int $outfitId, array $itemData): bool
    {
        try {
            // Get product details with category name
            $sql = "SELECT p.*, c.name as category_name
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.id = ?";
            $stmt = Database::execute($sql, [$itemData['product_id']]);
            $product = $stmt->fetch();

            if (!$product) {
                throw new \Exception('Product not found');
            }

            // Check if item already exists in outfit
            $existingItem = $this->findOutfitItem($outfitId, $itemData['product_id']);
            if ($existingItem) {
                throw new \Exception('Item already in outfit');
            }

            $sql = "INSERT INTO outfit_items (
                        outfit_id, product_id, selected_color, selected_size, selected_color_hex,
                        category_type, sort_order, is_primary, price_at_addition, sale_price_at_addition,
                        styling_notes, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

            // Get color hex value if color is provided
            $colorHex = null;
            if (!empty($itemData['selected_color'])) {
                $colorHex = $itemData['selected_color_hex'] ?? $this->getColorHex($itemData['selected_color']);
            }

            $params = [
                $outfitId,
                $itemData['product_id'],
                $itemData['selected_color'] ?? null,
                $itemData['selected_size'] ?? null,
                $colorHex,
                $itemData['category_type'] ?? $this->getCategoryType($product['category_name'] ?? 'top'),
                $itemData['sort_order'] ?? 0,
                $itemData['is_primary'] ?? false,
                $product['price'],
                $product['sale_price'],
                $itemData['styling_notes'] ?? null
            ];

            $statement = Database::execute($sql, $params);
            $result = $statement->rowCount() > 0;

            // Update outfit totals after adding item
            if ($result) {
                $this->updateOutfitTotals($outfitId);
            }

            return $result;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Generate UUID for outfit
     */
    private function generateUuid(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Remove item from outfit
     */
    public function removeItemFromOutfit(int $outfitId, int $itemId): bool
    {
        try {
            $sql = "DELETE FROM outfit_items WHERE id = ? AND outfit_id = ?";
            $statement = Database::execute($sql, [$itemId, $outfitId]);
            $result = $statement->rowCount() > 0;

            if ($result) {
                $this->updateOutfitTotals($outfitId);
            }

            return $result;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Update outfit totals
     */
    public function updateOutfitTotals(int $outfitId): bool
    {
        try {
            $sql = "SELECT
                        SUM(price_at_addition) as total_price,
                        SUM(COALESCE(sale_price_at_addition, price_at_addition)) as total_sale_price,
                        COUNT(*) as item_count
                    FROM outfit_items
                    WHERE outfit_id = ?";

            $statement = Database::execute($sql, [$outfitId]);
            $totals = $statement->fetch();

            $totalPrice = $totals['total_price'] ?? 0.00;
            $totalSalePrice = $totals['total_sale_price'] ?? 0.00;
            $itemCount = $totals['item_count'] ?? 0;

            // Mark outfit as complete if it has items
            $isComplete = $itemCount > 0;

            $updateSql = "UPDATE {$this->table} SET
                            total_price = ?,
                            total_sale_price = ?,
                            is_complete = ?,
                            updated_at = NOW()
                          WHERE id = ?";

            $updateStatement = Database::execute($updateSql, [
                $totalPrice,
                $totalSalePrice,
                $isComplete,
                $outfitId
            ]);

            return $updateStatement->rowCount() > 0;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Delete outfit
     */
    public function deleteOutfit(int $outfitId): bool
    {
        try {
            // Delete outfit items first (cascade should handle this, but being explicit)
            Database::execute("DELETE FROM outfit_items WHERE outfit_id = ?", [$outfitId]);

            // Delete outfit
            $statement = Database::execute("DELETE FROM {$this->table} WHERE id = ?", [$outfitId]);

            return $statement->rowCount() > 0;

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Find outfit item
     */
    private function findOutfitItem(int $outfitId, int $productId): ?array
    {
        try {
            $sql = "SELECT * FROM outfit_items WHERE outfit_id = ? AND product_id = ?";
            $statement = Database::execute($sql, [$outfitId, $productId]);
            $result = $statement->fetch();
            return $result ?: null;

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get color hex value from color name
     */
    private function getColorHex(string $colorName): ?string
    {
        // Common color mappings
        $colorMap = [
            // Basic colors
            'Black' => '#000000',
            'White' => '#FFFFFF',
            'Red' => '#DC2626',
            'Blue' => '#2563EB',
            'Green' => '#16A34A',
            'Yellow' => '#EAB308',
            'Orange' => '#EA580C',
            'Purple' => '#9333EA',
            'Pink' => '#EC4899',
            'Brown' => '#A16207',
            'Gray' => '#6B7280',
            'Grey' => '#6B7280',

            // Extended colors
            'Navy' => '#1E3A8A',
            'Navy Blue' => '#1E3A8A',
            'Light Blue' => '#3B82F6',
            'Dark Blue' => '#1E40AF',
            'Sky Blue' => '#0EA5E9',
            'Royal Blue' => '#2563EB',

            'Dark Green' => '#15803D',
            'Light Green' => '#22C55E',
            'Forest Green' => '#166534',
            'Olive' => '#84CC16',
            'Sage Green' => '#9CA3AF',

            'Maroon' => '#991B1B',
            'Burgundy' => '#7C2D12',
            'Wine' => '#7C2D12',

            'Beige' => '#F5F5DC',
            'Cream' => '#FFFDD0',
            'Ivory' => '#FFFFF0',
            'Off White' => '#FAF9F6',

            'Light Gray' => '#D1D5DB',
            'Dark Gray' => '#374151',
            'Charcoal' => '#1F2937',
            'Silver' => '#9CA3AF',

            'Light Wash' => '#6B9BD1',
            'Dark Wash' => '#1E3A8A',
            'Stone Wash' => '#9CA3AF',

            // Fashion colors
            'Khaki' => '#F0E68C',
            'Tan' => '#D2B48C',
            'Camel' => '#C19A6B',
            'Mustard' => '#FFDB58',
            'Rust' => '#B7410E',
            'Coral' => '#FF7F50',
            'Teal' => '#14B8A6',
            'Mint' => '#6EE7B7',
            'Lavender' => '#E879F9',
            'Rose' => '#F43F5E',
            'Peach' => '#FDBA74'
        ];

        // Try exact match first
        if (isset($colorMap[$colorName])) {
            return $colorMap[$colorName];
        }

        // Try case-insensitive match
        foreach ($colorMap as $name => $hex) {
            if (strcasecmp($name, $colorName) === 0) {
                return $hex;
            }
        }

        // Try to find from product_colors table
        try {
            $sql = "SELECT hex_value FROM product_colors WHERE name = ? LIMIT 1";
            $stmt = Database::execute($sql, [$colorName]);
            $result = $stmt->fetch();
            if ($result) {
                return $result['hex_value'];
            }
        } catch (\Exception $e) {
            // Fallback to default
        }

        // Default fallback color
        return '#6B7280'; // Gray
    }

    /**
     * Get category type from product category
     */
    private function getCategoryType(string $category): string
    {
        $categoryMap = [
            // Database category names (with spaces and proper case)
            'Oversized Tees' => 'top',
            'T-Shirts' => 'top',
            'Shirts' => 'top',
            'Hoodies' => 'outerwear',
            'Baggy Jeans' => 'bottom',
            'Fit Jeans' => 'bottom',
            'Capri' => 'bottom',
            'Jackets' => 'outerwear',
            'Shacket' => 'outerwear',
            'Full Sleeves Shirt' => 'top',
            // Slug versions (with dashes)
            'oversized-tees' => 'top',
            't-shirts' => 'top',
            'shirts' => 'top',
            'hoodies' => 'outerwear',
            'baggy-jeans' => 'bottom',
            'fit-jeans' => 'bottom',
            'capri' => 'bottom',
            'jackets' => 'outerwear',
            'shacket' => 'outerwear',
            'full-sleeves-shirt' => 'top',
            // Generic categories
            'jeans' => 'bottom',
            'pants' => 'bottom',
            'shorts' => 'bottom',
            'shoes' => 'footwear',
            'accessories' => 'accessory'
        ];

        return $categoryMap[$category] ?? 'top';
    }

    /**
     * Get user outfit count
     */
    public function getUserOutfitCount(int $userId): int
    {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE user_id = ?";
            $statement = Database::execute($sql, [$userId]);
            $result = $statement->fetch();
            return (int)($result['count'] ?? 0);

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Check if user owns outfit
     */
    public function isUserOutfit(int $outfitId, int $userId): bool
    {
        try {
            $sql = "SELECT 1 FROM {$this->table} WHERE id = ? AND user_id = ? LIMIT 1";
            $statement = Database::execute($sql, [$outfitId, $userId]);
            $result = $statement->fetch();
            return $result !== false;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Process outfit result
     */
    private function processOutfitResult(array $outfit): array
    {
        $outfit = $this->processResult($outfit);

        // Parse tags if they exist
        if (!empty($outfit['tags'])) {
            $outfit['tags'] = explode(',', $outfit['tags']);
        } else {
            $outfit['tags'] = [];
        }

        return $outfit;
    }
}
