<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\User;
use Wolffoxx\Middleware\AuthMiddleware;
use <PERSON>oxx\Utils\Response;
use Wolffoxx\Utils\Logger;
use Wolffoxx\Utils\Validator;

/**
 * User Controller
 * 
 * Handles user profile management, settings,
 * and user-related operations.
 */
class UserController
{
    private User $userModel;
    private Logger $logger;

    public function __construct()
    {
        $this->userModel = new User();
        $this->logger = new Logger('user');
    }

    /**
     * Get user profile
     */
    public function getProfile(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $profile = $this->userModel->getProfile($userId);

            if (!$profile) {
                Response::notFound('User profile not found');
                return;
            }

            $this->logger->info('Profile retrieved', [
                'user_id' => $userId
            ]);

            Response::success($profile);

        } catch (\Exception $e) {
            $this->logger->error('Get profile failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to retrieve profile');
        }
    }

    /**
     * Update user profile
     */
    public function updateProfile(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'first_name' => 'nullable|string|max:100',
                'last_name' => 'nullable|string|max:100',
                'phone' => 'nullable|string|max:20',
                'date_of_birth' => 'nullable|date',
                'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
                'newsletter_subscribed' => 'nullable|boolean',
                'marketing_emails' => 'nullable|boolean',
                'sms_notifications' => 'nullable|boolean'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Update profile
            $updatedProfile = $this->userModel->updateProfile($userId, $data);

            if (!$updatedProfile) {
                Response::error('Failed to update profile');
                return;
            }

            $this->logger->info('Profile updated', [
                'user_id' => $userId,
                'updated_fields' => array_keys($data)
            ]);

            Response::success([
                'profile' => $updatedProfile,
                'message' => 'Profile updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Update profile failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to update profile');
        }
    }

    /**
     * Upload profile image
     */
    public function uploadProfileImage(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Check if file was uploaded
            if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
                Response::error('No image file uploaded or upload error occurred', 400);
                return;
            }

            $file = $_FILES['image'];

            // Validate file
            $validator = $this->validateImageFile($file);
            if (!$validator['valid']) {
                Response::error($validator['error'], 400);
                return;
            }

            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'profile_' . $userId . '_' . time() . '.' . $extension;
            $uploadPath = __DIR__ . '/../../storage/uploads/profiles/';
            
            // Ensure upload directory exists
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $fullPath = $uploadPath . $filename;

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
                Response::error('Failed to save uploaded file');
                return;
            }

            // Update user profile with image path
            $imageUrl = '/storage/uploads/profiles/' . $filename;
            $this->userModel->updateProfile($userId, ['profile_image' => $imageUrl]);

            $this->logger->info('Profile image uploaded', [
                'user_id' => $userId,
                'filename' => $filename
            ]);

            Response::success([
                'image_url' => $imageUrl,
                'message' => 'Profile image uploaded successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Upload profile image failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to upload profile image');
        }
    }

    /**
     * Delete profile image
     */
    public function deleteProfileImage(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Get current profile
            $profile = $this->userModel->findById($userId);
            
            if (!$profile || !$profile['profile_image']) {
                Response::error('No profile image to delete', 400);
                return;
            }

            // Delete file if it exists
            $imagePath = __DIR__ . '/../../' . ltrim($profile['profile_image'], '/');
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }

            // Update profile to remove image
            $this->userModel->updateProfile($userId, ['profile_image' => null]);

            $this->logger->info('Profile image deleted', [
                'user_id' => $userId
            ]);

            Response::success([
                'message' => 'Profile image deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Delete profile image failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to delete profile image');
        }
    }

    /**
     * Change password
     */
    public function changePassword(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'current_password' => 'required|string',
                'new_password' => 'required|min:8|max:255',
                'new_password_confirmation' => 'required|same:new_password'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Get user
            $user = $this->userModel->findById($userId);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }

            // Verify current password
            if (!$this->userModel->verifyPassword($data['current_password'], $user['password_hash'])) {
                Response::error('Current password is incorrect', 400);
                return;
            }

            // Update password
            $updated = $this->userModel->updatePassword($userId, $data['new_password']);

            if (!$updated) {
                Response::error('Failed to update password');
                return;
            }

            $this->logger->info('Password changed', [
                'user_id' => $userId
            ]);

            Response::success([
                'message' => 'Password changed successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Change password failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to change password');
        }
    }

    /**
     * Validate uploaded image file
     */
    private function validateImageFile(array $file): array
    {
        $maxSize = (int)($_ENV['UPLOAD_MAX_SIZE'] ?? 5242880); // 5MB default
        $allowedTypes = explode(',', $_ENV['UPLOAD_ALLOWED_TYPES'] ?? 'jpg,jpeg,png,gif,webp');

        // Check file size
        if ($file['size'] > $maxSize) {
            return [
                'valid' => false,
                'error' => 'File size exceeds maximum allowed size of ' . ($maxSize / 1024 / 1024) . 'MB'
            ];
        }

        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            return [
                'valid' => false,
                'error' => 'File type not allowed. Allowed types: ' . implode(', ', $allowedTypes)
            ];
        }

        // Check if it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return [
                'valid' => false,
                'error' => 'File is not a valid image'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Get JSON input from request body
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Response::error('Invalid JSON input', 400);
            exit;
        }

        return $data ?? [];
    }
}
