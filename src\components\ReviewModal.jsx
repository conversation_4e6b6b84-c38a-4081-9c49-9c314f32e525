import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, X, Send, User } from 'lucide-react';

const ReviewModal = ({ isOpen, onClose, productName, onSubmit }) => {
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [reviewText, setReviewText] = useState('');
  const [userName, setUserName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (rating === 0) {
      newErrors.rating = 'Please select a rating';
    }

    if (!reviewText.trim()) {
      newErrors.reviewText = 'Please write a review';
    } else if (reviewText.trim().length < 10) {
      newErrors.reviewText = 'Review must be at least 10 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const reviewData = {
        rating,
        text: reviewText.trim(),
        userName: userName.trim() || 'Anonymous',
        date: new Date().toISOString(),
        productName
      };

      await onSubmit(reviewData);

      // Reset form
      setRating(0);
      setReviewText('');
      setUserName('');
      setErrors({});
      onClose();
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setRating(0);
    setHoverRating(0);
    setReviewText('');
    setUserName('');
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="relative w-full max-w-md bg-[#1a1a1a] backdrop-blur-xl border border-[#404040] rounded-xl overflow-hidden max-h-[90vh] flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-[#2a2a2a] flex-shrink-0">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-[#FF6B35] to-[#F7931E]">
                <Star size={16} className="text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-white">Write a Review</h2>
                <p className="text-[#AAAAAA] text-xs line-clamp-1">{productName}</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="p-1.5 text-[#AAAAAA] hover:text-white transition-colors rounded-lg hover:bg-[#2a2a2a]"
            >
              <X size={20} />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-4 space-y-4 flex-1 overflow-y-auto">
            {/* Star Rating */}
            <div className="space-y-1.5">
              <label className="block text-white font-medium text-sm">
                Rating <span className="text-red-400">*</span>
              </label>
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    className="p-0.5 transition-transform hover:scale-110 focus:outline-none focus:ring-1 focus:ring-blue-500 rounded"
                    onClick={() => setRating(star)}
                    onMouseEnter={() => setHoverRating(star)}
                    onMouseLeave={() => setHoverRating(0)}
                  >
                    <Star
                      size={24}
                      className={`transition-colors ${
                        star <= (hoverRating || rating)
                          ? 'text-yellow-400 fill-yellow-400'
                          : 'text-slate-600'
                      }`}
                    />
                  </button>
                ))}
                {rating > 0 && (
                  <span className="ml-2 text-[#AAAAAA] text-xs">
                    {rating} star{rating !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
              {errors.rating && (
                <p className="text-red-400 text-xs">{errors.rating}</p>
              )}
            </div>

            {/* Review Text */}
            <div className="space-y-1.5">
              <label className="block text-white font-medium text-sm">
                Your Review <span className="text-red-400">*</span>
              </label>
              <textarea
                value={reviewText}
                onChange={(e) => setReviewText(e.target.value)}
                placeholder="Share your experience with this product..."
                rows={3}
                className={`w-full bg-[#2a2a2a] border rounded-lg px-3 py-2 text-white placeholder-[#AAAAAA] focus:outline-none transition-colors resize-none text-sm ${
                  errors.reviewText
                    ? 'border-red-500 focus:border-red-500'
                    : 'border-[#404040] focus:border-blue-500'
                }`}
              />
              <div className="flex justify-between items-center">
                {errors.reviewText && (
                  <p className="text-red-400 text-xs">{errors.reviewText}</p>
                )}
                <p className="text-[#AAAAAA] text-xs ml-auto">
                  {reviewText.length}/500 characters
                </p>
              </div>
            </div>

            {/* User Name */}
            <div className="space-y-1.5">
              <label className="block text-white font-medium text-sm">
                Your Name <span className="text-[#AAAAAA] text-xs">(optional)</span>
              </label>
              <div className="relative">
                <User size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#AAAAAA]" />
                <input
                  type="text"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="Enter your name or leave blank"
                  className="w-full bg-[#2a2a2a] border border-[#404040] rounded-lg pl-9 pr-3 py-2 text-white placeholder-[#AAAAAA] focus:border-blue-500 focus:outline-none transition-colors text-sm"
                  maxLength={50}
                />
              </div>
            </div>
          </form>

          {/* Buttons - Fixed Footer */}
          <div className="border-t border-[#2a2a2a] p-4 flex-shrink-0">
            <div className="flex flex-col sm:flex-row gap-2">
              <button
                type="button"
                onClick={handleClose}
                className="flex-1 bg-[#404040] hover:bg-[#6a6a6a] text-white py-2.5 px-4 rounded-lg font-medium transition-all duration-300 text-sm"
              >
                Cancel
              </button>
              <motion.button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="flex-1 text-white py-2.5 px-4 rounded-lg font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                style={{
                  background: isSubmitting ? '#64748b' : 'linear-gradient(to bottom right, #FF6B35 0%, #F7931E 100%)'
                }}
                whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send size={16} />
                    Submit Review
                  </>
                )}
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ReviewModal;
