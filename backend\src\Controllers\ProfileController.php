<?php

namespace Wolffoxx\Controllers;

use Wolffoxx\Models\User;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Auth;

/**
 * Profile Controller
 * 
 * Handles user profile completion and validation
 */
class ProfileController
{
    private User $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * Check profile completion status
     * GET /api/v1/profile/completion-status
     */
    public function getCompletionStatus(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $status = $this->userModel->getProfileCompletionStatus($user['id']);

            Response::success([
                'is_complete' => $status['is_complete'],
                'missing_fields' => $status['missing_fields'],
                'user' => $status['user'],
                'requires_modal' => !$status['is_complete']
            ]);

        } catch (\Exception $e) {
            error_log('Get profile completion status failed: ' . $e->getMessage());
            Response::error('Failed to get profile status', 500);
        }
    }

    /**
     * Complete user profile (modal submission)
     * POST /api/v1/profile/complete
     */
    public function completeProfile(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Validate required fields
            if (empty($input['name']) || empty($input['email'])) {
                Response::error('Name and email are required', 400);
                return;
            }

            // Validate name length
            if (strlen(trim($input['name'])) < 2) {
                Response::error('Name must be at least 2 characters', 400);
                return;
            }

            // Validate email format
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                Response::error('Invalid email format', 400);
                return;
            }

            // Complete profile
            $success = $this->userModel->completeProfile(
                $user['id'],
                $input['name'],
                $input['email']
            );

            if ($success) {
                // Get updated user data
                $updatedStatus = $this->userModel->getProfileCompletionStatus($user['id']);

                Response::success([
                    'message' => 'Profile completed successfully',
                    'is_complete' => $updatedStatus['is_complete'],
                    'user' => $updatedStatus['user']
                ]);
            } else {
                Response::error('Failed to complete profile', 500);
            }

        } catch (\InvalidArgumentException $e) {
            Response::error($e->getMessage(), 400);
        } catch (\Exception $e) {
            error_log('Complete profile failed: ' . $e->getMessage());
            Response::error('Failed to complete profile', 500);
        }
    }

    /**
     * Validate user for order placement
     * GET /api/v1/profile/validate-for-order
     */
    public function validateForOrder(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $status = $this->userModel->getProfileCompletionStatus($user['id']);

            // Check if profile is complete
            if (!$status['is_complete']) {
                Response::error('Profile incomplete. Please complete your profile before placing an order.', 400, [
                    'missing_fields' => $status['missing_fields'],
                    'requires_profile_completion' => true
                ]);
                return;
            }

            // Check if phone is verified (assuming phone verification is done during OTP)
            if (empty($user['phone'])) {
                Response::error('Phone number verification required', 400, [
                    'requires_phone_verification' => true
                ]);
                return;
            }

            Response::success([
                'message' => 'User profile is valid for order placement',
                'user' => $status['user'],
                'can_place_order' => true
            ]);

        } catch (\Exception $e) {
            error_log('Validate for order failed: ' . $e->getMessage());
            Response::error('Failed to validate user profile', 500);
        }
    }

    /**
     * Update user profile
     * PUT /api/v1/profile
     */
    public function updateProfile(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Validate inputs if provided
            if (isset($input['name']) && strlen(trim($input['name'])) < 2) {
                Response::error('Name must be at least 2 characters', 400);
                return;
            }

            if (isset($input['email']) && !filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                Response::error('Invalid email format', 400);
                return;
            }

            // Check if email is already taken by another user
            if (isset($input['email'])) {
                $existingUser = $this->userModel->findByEmail($input['email']);
                if ($existingUser && $existingUser['id'] !== $user['id']) {
                    Response::error('Email already exists', 400);
                    return;
                }
            }

            // Prepare update data
            $updateData = [];
            
            if (isset($input['name'])) {
                $nameParts = explode(' ', trim($input['name']), 2);
                $updateData['first_name'] = $nameParts[0];
                $updateData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
            }

            if (isset($input['email'])) {
                $updateData['email'] = $input['email'];
            }

            if (isset($input['date_of_birth'])) {
                $updateData['date_of_birth'] = $input['date_of_birth'];
            }

            if (isset($input['gender'])) {
                $updateData['gender'] = $input['gender'];
            }

            // Update profile completion status
            $isComplete = $this->userModel->isProfileComplete($user['id']);
            if ($isComplete) {
                $updateData['profile_completed'] = 1;
                $updateData['profile_completed_at'] = date('Y-m-d H:i:s');
            }

            if (empty($updateData)) {
                Response::error('No valid fields to update', 400);
                return;
            }

            $updatedUser = $this->userModel->updateProfile($user['id'], $updateData);

            if ($updatedUser) {
                Response::success([
                    'message' => 'Profile updated successfully',
                    'user' => $updatedUser
                ]);
            } else {
                Response::error('Failed to update profile', 500);
            }

        } catch (\Exception $e) {
            error_log('Update profile failed: ' . $e->getMessage());
            Response::error('Failed to update profile', 500);
        }
    }

    /**
     * Get user profile
     * GET /api/v1/profile
     */
    public function getProfile(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $profile = $this->userModel->getProfile($user['id']);

            if ($profile) {
                // Add completion status
                $completionStatus = $this->userModel->getProfileCompletionStatus($user['id']);
                $profile['profile_completion'] = $completionStatus;

                Response::success($profile);
            } else {
                Response::error('Profile not found', 404);
            }

        } catch (\Exception $e) {
            error_log('Get profile failed: ' . $e->getMessage());
            Response::error('Failed to get profile', 500);
        }
    }
}
