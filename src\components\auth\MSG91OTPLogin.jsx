import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Phone, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

// Simple API request function
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;

  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, config);
  const data = await response.json();

  if (!response.ok) {
    throw new Error(data?.message || data?.error || `HTTP ${response.status}`);
  }

  return data;
};

const MSG91OTPLogin = ({ onSuccess, onClose }) => {
  const [step, setStep] = useState('phone'); // 'phone' | 'widget'
  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [widgetConfig, setWidgetConfig] = useState(null);
  const { } = useAuth();

  // Format phone number display
  const formatPhoneDisplay = (value) => {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length <= 10) {
      return cleaned.replace(/(\d{5})(\d{5})/, '$1 $2');
    }
    return cleaned;
  };

  // Handle phone input
  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, '');
    if (value.length <= 10) {
      setPhone(value);
      setError('');
    }
  };

  // Validate phone number
  const validatePhone = (phone) => {
    return /^[6-9]\d{9}$/.test(phone);
  };

  // Load widget configuration
  const loadWidgetConfig = async () => {
    try {
      setIsLoading(true);
      const response = await apiRequest('/auth/otp/widget-config');
      if (response.success) {
        setWidgetConfig(response.data);
        return true;
      } else {
        setError('Failed to load OTP service');
        return false;
      }
    } catch (error) {
      console.error('Widget config error:', error);
      setError('Failed to load OTP service');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize MSG91 Widget
  const initializeMSG91Widget = (config) => {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (window.initSendOTP) {
        setupWidget(config, resolve, reject);
        return;
      }

      // Load MSG91 script
      const script = document.createElement('script');
      script.src = config.scriptUrl;
      script.type = 'text/javascript';
      script.onload = () => {
        setupWidget(config, resolve, reject);
      };
      script.onerror = () => {
        reject(new Error('Failed to load MSG91 script'));
      };
      document.body.appendChild(script);
    });
  };

  // Setup widget configuration
  const setupWidget = (config, resolve, reject) => {
    try {
      const formattedPhone = phone.length === 10 ? '91' + phone : phone;
      
      const configuration = {
        widgetId: config.widgetId,
        tokenAuth: config.authKey,
        identifier: formattedPhone,
        exposeMethods: true,
        success: (data) => {
          console.log('MSG91 Widget Success:', data);
          handleWidgetSuccess(data);
          resolve(data);
        },
        failure: (error) => {
          console.error('MSG91 Widget Error:', error);
          setError('OTP verification failed: ' + (error.message || 'Unknown error'));
          reject(error);
        }
      };

      // Initialize the widget
      if (window.initSendOTP) {
        window.initSendOTP(configuration);
      } else {
        reject(new Error('MSG91 widget not available'));
      }
    } catch (error) {
      console.error('Widget setup error:', error);
      reject(error);
    }
  };

  // Handle widget success
  const handleWidgetSuccess = async (data) => {
    try {
      setIsLoading(true);
      
      // Extract access token from widget response
      const accessToken = data.access_token || data.accessToken || data.token;
      
      if (!accessToken) {
        setError('No access token received from OTP verification');
        return;
      }

      // Verify token with our backend
      const response = await apiRequest('/auth/otp/verify', {
        method: 'POST',
        body: JSON.stringify({
          access_token: accessToken
        })
      });

      if (response.success) {
        onSuccess(response.data);
      } else {
        setError(response.message || 'Token verification failed');
      }
    } catch (error) {
      console.error('Token verification error:', error);
      setError('Failed to verify OTP token');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle start OTP process
  const handleStartOTP = async () => {
    setError('');

    // Validate phone
    if (!validatePhone(phone)) {
      setError('Please enter a valid 10-digit phone number');
      return;
    }

    // Load widget config and initialize
    const configLoaded = await loadWidgetConfig();
    if (configLoaded && widgetConfig) {
      try {
        setStep('widget');
        await initializeMSG91Widget(widgetConfig);
      } catch (error) {
        console.error('Widget initialization error:', error);
        setError('Failed to initialize OTP service');
        setStep('phone');
      }
    }
  };

  // Send OTP using widget
  const sendOTP = () => {
    if (window.sendOtp && phone) {
      const formattedPhone = phone.length === 10 ? '91' + phone : phone;
      window.sendOtp(
        formattedPhone,
        (data) => {
          console.log('OTP sent successfully:', data);
        },
        (error) => {
          console.error('OTP send error:', error);
          setError('Failed to send OTP: ' + (error.message || 'Unknown error'));
        }
      );
    } else {
      setError('OTP service not available');
    }
  };

  // Verify OTP using widget
  const verifyOTP = (otp) => {
    if (window.verifyOtp && otp) {
      window.verifyOtp(
        otp,
        (data) => {
          console.log('OTP verified successfully:', data);
          handleWidgetSuccess(data);
        },
        (error) => {
          console.error('OTP verify error:', error);
          setError('Invalid OTP: ' + (error.message || 'Please try again'));
        }
      );
    } else {
      setError('OTP verification service not available');
    }
  };

  // Retry OTP using widget
  const retryOTP = () => {
    if (window.retryOtp) {
      window.retryOtp(
        null, // Use default channel
        (data) => {
          console.log('OTP resent successfully:', data);
        },
        (error) => {
          console.error('OTP retry error:', error);
          setError('Failed to resend OTP: ' + (error.message || 'Unknown error'));
        }
      );
    } else {
      setError('OTP retry service not available');
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {step === 'phone' && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-2">Welcome to Wolffoxx</h2>
            <p className="text-[#AAAAAA]">Enter your phone number to continue</p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
                Phone Number
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone size={18} className="text-[#6a6a6a]" />
                  <span className="ml-2 text-[#AAAAAA]">+91</span>
                </div>
                <input
                  type="tel"
                  value={formatPhoneDisplay(phone)}
                  onChange={handlePhoneChange}
                  placeholder="98765 43210"
                  className="w-full pl-16 pr-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white placeholder-[#6a6a6a] focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                  style={{ minHeight: '44px' }}
                />
              </div>
            </div>

            <motion.button
              onClick={handleStartOTP}
              disabled={isLoading || !phone}
              className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-[#404040] disabled:to-[#404040] text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
              style={{ minHeight: '44px' }}
              whileHover={{ scale: phone && !isLoading ? 1.02 : 1 }}
              whileTap={{ scale: phone && !isLoading ? 0.98 : 1 }}
            >
              {isLoading ? (
                <Loader2 size={18} className="animate-spin" />
              ) : (
                <>
                  Continue with OTP
                  <CheckCircle size={18} />
                </>
              )}
            </motion.button>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm flex items-center gap-2"
            >
              <AlertCircle size={16} />
              {error}
            </motion.div>
          )}
        </motion.div>
      )}

      {step === 'widget' && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-2">Verify Your Phone</h2>
            <p className="text-[#AAAAAA] mb-4">
              We'll send an OTP to +91 {formatPhoneDisplay(phone)}
            </p>
          </div>

          <div className="space-y-4">
            <button
              onClick={sendOTP}
              className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200"
            >
              Send OTP
            </button>

            <div className="text-center">
              <span className="text-[#AAAAAA]">or</span>
            </div>

            <OTPInput onVerify={verifyOTP} onRetry={retryOTP} />

            <button
              onClick={() => setStep('phone')}
              className="w-full text-[#AAAAAA] hover:text-white py-2 text-sm transition-colors"
            >
              ← Change Phone Number
            </button>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm flex items-center gap-2"
            >
              <AlertCircle size={16} />
              {error}
            </motion.div>
          )}
        </motion.div>
      )}
    </div>
  );
};

// Simple OTP Input Component
const OTPInput = ({ onVerify, onRetry }) => {
  const [otp, setOtp] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (otp.length === 6) {
      onVerify(otp);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
          Enter 6-digit OTP
        </label>
        <input
          type="text"
          value={otp}
          onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
          placeholder="000000"
          className="w-full px-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white placeholder-[#6a6a6a] focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-center text-lg font-mono"
          maxLength={6}
        />
      </div>

      <div className="flex space-x-3">
        <button
          type="submit"
          disabled={otp.length !== 6}
          className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:bg-[#404040] disabled:cursor-not-allowed transition-colors"
        >
          Verify OTP
        </button>
        
        <button
          type="button"
          onClick={onRetry}
          className="flex-1 bg-[#404040] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#505050] transition-colors"
        >
          Resend OTP
        </button>
      </div>
    </form>
  );
};

export default MSG91OTPLogin;
