<?php

namespace Wolffoxx\Models;

use Wolffoxx\Config\Database;

/**
 * Cart Model
 * 
 * Handles shopping cart operations, cart items,
 * and cart-related database interactions.
 */
class Cart extends BaseModel
{
    protected string $table = 'carts';
    
    protected array $fillable = [
        'user_id',
        'session_id',
        'total_items',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'coupon_code',
        'coupon_discount',
        'status'
    ];

    protected array $casts = [
        'user_id' => 'integer',
        'total_items' => 'integer',
        'subtotal' => 'float',
        'tax_amount' => 'float',
        'discount_amount' => 'float',
        'total_amount' => 'float',
        'coupon_discount' => 'float'
    ];

    /**
     * Get or create cart for user
     */
    public function getOrCreateUserCart(int $userId): array
    {
        $sql = "SELECT * FROM carts WHERE user_id = ? AND status = 'active' ORDER BY updated_at DESC LIMIT 1";
        $statement = Database::execute($sql, [$userId]);
        $cart = $statement->fetch();

        if (!$cart) {
            $cartId = $this->createCart($userId);
            $cart = $this->findById($cartId);
        }

        return $cart ?: [];
    }

    /**
     * Create new cart
     */
    public function createCart(int $userId, ?string $sessionId = null): int
    {
        $sql = "INSERT INTO carts (user_id, session_id, status, created_at, updated_at) 
                VALUES (?, ?, 'active', NOW(), NOW())";
        
        Database::execute($sql, [$userId, $sessionId]);
        return Database::lastInsertId();
    }

    /**
     * Get cart with items
     */
    public function getCartWithItems(int $cartId): array
    {
        $cart = $this->findById($cartId);
        if (!$cart) {
            return [];
        }

        $items = $this->getCartItems($cartId);
        $cart['items'] = $items;

        return $cart;
    }

    /**
     * Get cart items with product details
     */
    public function getCartItems(int $cartId): array
    {
        $sql = "SELECT 
                    ci.*,
                    p.name as product_name,
                    p.slug as product_slug,
                    p.category,
                    p.price as current_price,
                    p.sale_price as current_sale_price,
                    p.stock_quantity,
                    pi.image_url as product_image
                FROM cart_items ci
                JOIN products p ON ci.product_id = p.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                WHERE ci.cart_id = ?
                ORDER BY ci.created_at ASC";
        
        $statement = Database::execute($sql, [$cartId]);
        return $statement->fetchAll();
    }

    /**
     * Add item to cart
     */
    public function addItem(int $cartId, array $itemData): bool
    {
        // Check if item already exists
        $existingItem = $this->findCartItem(
            $cartId, 
            $itemData['product_id'], 
            $itemData['selected_color'] ?? null,
            $itemData['selected_size'] ?? null
        );
        
        if ($existingItem) {
            // Update quantity
            return $this->updateItemQuantity(
                $existingItem['id'], 
                $existingItem['quantity'] + ($itemData['quantity'] ?? 1)
            );
        }
        
        // Add new item
        $sql = "INSERT INTO cart_items (
                    cart_id, product_id, selected_color, selected_size, selected_color_hex,
                    quantity, unit_price, sale_price, total_price, product_name, 
                    product_image, product_sku, outfit_id, outfit_name,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $totalPrice = ($itemData['sale_price'] ?? $itemData['unit_price']) * $itemData['quantity'];
        
        $statement = Database::execute($sql, [
            $cartId,
            $itemData['product_id'],
            $itemData['selected_color'] ?? null,
            $itemData['selected_size'] ?? null,
            $itemData['selected_color_hex'] ?? null,
            $itemData['quantity'] ?? 1,
            $itemData['unit_price'],
            $itemData['sale_price'] ?? null,
            $totalPrice,
            $itemData['product_name'],
            $itemData['product_image'] ?? null,
            $itemData['product_sku'] ?? null,
            $itemData['outfit_id'] ?? null,
            $itemData['outfit_name'] ?? null
        ]);

        $result = $statement->rowCount() > 0;

        if ($result) {
            $this->updateCartTotals($cartId);
        }

        return $result;
    }

    /**
     * Find cart item
     */
    private function findCartItem(int $cartId, int $productId, ?string $color = null, ?string $size = null): ?array
    {
        $sql = "SELECT * FROM cart_items 
                WHERE cart_id = ? AND product_id = ? AND selected_color = ? AND selected_size = ?";
        
        $statement = Database::execute($sql, [$cartId, $productId, $color, $size]);
        $result = $statement->fetch();
        return $result ?: null;
    }

    /**
     * Update item quantity
     */
    public function updateItemQuantity(int $itemId, int $quantity): bool
    {
        if ($quantity <= 0) {
            return $this->removeItem($itemId);
        }
        
        // Get item details for total calculation
        $statement = Database::execute("SELECT * FROM cart_items WHERE id = ?", [$itemId]);
        $item = $statement->fetch();
        if (!$item) {
            return false;
        }
        
        $unitPrice = $item['sale_price'] ?? $item['unit_price'];
        $totalPrice = $unitPrice * $quantity;
        
        $sql = "UPDATE cart_items SET quantity = ?, total_price = ?, updated_at = NOW() WHERE id = ?";
        $statement = Database::execute($sql, [$quantity, $totalPrice, $itemId]);
        $result = $statement->rowCount() > 0;

        if ($result) {
            $this->updateCartTotals($item['cart_id']);
        }

        return $result;
    }

    /**
     * Remove item from cart
     */
    public function removeItem(int $itemId): bool
    {
        $statement = Database::execute("SELECT cart_id FROM cart_items WHERE id = ?", [$itemId]);
        $item = $statement->fetch();
        if (!$item) {
            return false;
        }

        $deleteStatement = Database::execute("DELETE FROM cart_items WHERE id = ?", [$itemId]);
        $result = $deleteStatement->rowCount() > 0;

        if ($result) {
            $this->updateCartTotals($item['cart_id']);
        }

        return $result;
    }

    /**
     * Clear cart
     */
    public function clearCart(int $cartId): bool
    {
        $statement = Database::execute("DELETE FROM cart_items WHERE cart_id = ?", [$cartId]);
        $result = $statement->rowCount() > 0;

        if ($result) {
            $this->updateCartTotals($cartId);
        }

        return $result;
    }

    /**
     * Update cart totals
     */
    public function updateCartTotals(int $cartId): bool
    {
        $sql = "SELECT 
                    COUNT(*) as total_items,
                    SUM(total_price) as subtotal
                FROM cart_items 
                WHERE cart_id = ?";
        
        $statement = Database::execute($sql, [$cartId]);
        $totals = $statement->fetch();

        $totalItems = $totals['total_items'] ?? 0;
        $subtotal = $totals['subtotal'] ?? 0.00;
        $taxAmount = $subtotal * 0.18; // 18% GST
        $totalAmount = $subtotal + $taxAmount;

        $updateSql = "UPDATE carts SET
                        total_items = ?,
                        subtotal = ?,
                        tax_amount = ?,
                        total_amount = ?,
                        updated_at = NOW()
                      WHERE id = ?";

        $updateStatement = Database::execute($updateSql, [
            $totalItems,
            $subtotal,
            $taxAmount,
            $totalAmount,
            $cartId
        ]);

        return $updateStatement->rowCount() > 0;
    }

    /**
     * Get cart count for user
     */
    public function getUserCartCount(int $userId): int
    {
        $sql = "SELECT COALESCE(SUM(ci.quantity), 0) as total_count
                FROM carts c
                LEFT JOIN cart_items ci ON c.id = ci.cart_id
                WHERE c.user_id = ? AND c.status = 'active'";
        
        $statement = Database::execute($sql, [$userId]);
        $result = $statement->fetch();
        return (int)($result['total_count'] ?? 0);
    }

    /**
     * Apply coupon to cart
     */
    public function applyCoupon(int $cartId, string $couponCode, float $discountAmount): bool
    {
        $sql = "UPDATE carts SET 
                    coupon_code = ?, 
                    coupon_discount = ?, 
                    discount_amount = ?,
                    updated_at = NOW()
                WHERE id = ?";
        
        $statement = Database::execute($sql, [$couponCode, $discountAmount, $discountAmount, $cartId]);
        $result = $statement->rowCount() > 0;

        if ($result) {
            $this->updateCartTotals($cartId);
        }

        return $result;
    }

    /**
     * Remove coupon from cart
     */
    public function removeCoupon(int $cartId): bool
    {
        $sql = "UPDATE carts SET 
                    coupon_code = NULL, 
                    coupon_discount = 0, 
                    discount_amount = 0,
                    updated_at = NOW()
                WHERE id = ?";
        
        $statement = Database::execute($sql, [$cartId]);
        $result = $statement->rowCount() > 0;

        if ($result) {
            $this->updateCartTotals($cartId);
        }

        return $result;
    }

    /**
     * Get user cart with items
     */
    public function getUserCart(int $userId): array
    {
        $cart = $this->getOrCreateUserCart($userId);
        if (!$cart) {
            return [];
        }

        $items = $this->getCartItems($cart['id']);
        $cart['items'] = $items;

        return $cart;
    }
}
