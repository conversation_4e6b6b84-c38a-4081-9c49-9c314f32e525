<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./src/assets/logo40.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>WOLFFOXX</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            fontFamily: {
              'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
              'bebas': ['<PERSON><PERSON> Neue', 'sans-serif'],
            },
            animation: {
              'pulse-glow': 'pulse-glow 2s infinite',
            },
            keyframes: {
              'pulse-glow': {
                '0%, 100%': {
                  'box-shadow': '0 0 5px 0 rgba(99, 102, 241, 0.4)',
                },
                '50%': {
                  'box-shadow': '0 0 15px 0 rgba(99, 102, 241, 0.7)',
                },
              },
            },
          },
        },
      }
    </script>
    <style>
      /* Custom scrollbar for dark theme */
      ::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }

      ::-webkit-scrollbar-track {
        background-color: #1a1a1a;
      }

      ::-webkit-scrollbar-thumb {
        background-color: #404040;
        border-radius: 9999px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background-color: #6a6a6a;
      }

      /* Basic styles */
      html.dark {
        color-scheme: dark;
      }

      body {
        background-color: #000000;
        color: #d4d4d4;
        overflow-x: hidden;
        width: 100%;
        max-width: 100vw;
      }

      /* Prevent zoom issues */
      * {
        box-sizing: border-box;
      }

      html, body, #root {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
      }

      /* Product card animations */
      .product-card img {
        transition: transform 0.5s ease;
      }

      .product-card:hover img {
        transform: scale(1.05);
      }

      .product-card .arrow-button {
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
      }

      .product-card:hover .arrow-button {
        opacity: 1;
        transform: translateY(0);
      }

      .color-dot {
        transition: all 0.2s ease;
        border: 2px solid transparent;
      }

      .color-dot.active {
        transform: scale(1.25);
        border-color: #000;
      }

      /* Hide scrollbar utility */
      .scrollbar-hide {
        -ms-overflow-style: none;  /* Internet Explorer 10+ */
        scrollbar-width: none;  /* Firefox */
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Safari and Chrome */
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
